from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.integration.asr.tencent_asr import TencentASR
from omni.integration.oss.tencent_oss import oss_client


# 语音识别
@register_handler('asr')
class AsrApi:

    @auth_required(['admin', 'user'])
    async def long_text_recognition(self, data):
        file_key = data['file_key']
        url = await oss_client.signed_get_url(file_key)
        sentence = await TencentASR.getInstance().long_text_recognition(url)
        return sentence
