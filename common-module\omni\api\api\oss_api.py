from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.integration.oss.tencent_oss import oss_client
from omni.log.log import olog


# 腾讯对向存储
@register_handler('oss')
class OssApi:

    @auth_required(['user', 'admin'])
    async def gen_signed_url(self, data):
        olog.info(f"接收到的数据: {data}")
        signed_method = data.get('signed_method')
        signed_key = data.get('signed_key')

        if not signed_key:
            raise ValueError("signed_key 参数不能为空")

        signed_url_result = await oss_client.signed_url(signed_method, signed_key)
        return signed_url_result
