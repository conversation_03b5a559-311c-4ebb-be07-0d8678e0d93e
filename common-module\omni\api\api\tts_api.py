from omni.api.auth import auth_required
from omni.api.handler_register import register_handler
from omni.integration.tts.tencent_tts import TencentTTS


# 腾讯对向存储
@register_handler('tts')
class TtsApi:

    @auth_required(['admin', 'user'])
    async def long_text_to_voice(self, data):
        text = data['text']
        result = await TencentTTS.getInstance().long_text_to_voice(text)
        return result
