"""
oauth2:
  secret_key: iw2bNCc8ZR5INj78
"""
import importlib
import json
import time

from jose import jwt, JWTError

from omni.config.config_loader import config_dict
from omni.redis.redis_client import rc

User = importlib.import_module('models.models').User


def generate_token(user_id):
    payload = {
        'sub': user_id,
        'iat': int(time.time())
    }
    secret_key = config_dict['oauth2']['secret_key']
    token = jwt.encode(payload, secret_key, algorithm='HS256')
    return token


async def authenticate_user(token):
    if not token:
        return None, None

    try:
        secret_key = config_dict['oauth2']['secret_key']
        payload = jwt.decode(token, secret_key, algorithms=['HS256'], options={'verify_exp': False})
        user_id = payload.get('sub')
    except JWTError:
        return None, None

    user_roles = await rc.get(f'session:roles:{user_id}')
    if user_roles:
        user_roles = json.loads(user_roles)
        return user_id, user_roles

    user = await User.get(user_id)
    if not user:
        return None, None

    user_roles = user.roles
    await rc.set(f'session:roles:{user_id}', json.dumps(user_roles, ensure_ascii=False))
    return user_id, user_roles


class AccessResponse:
    def __init__(self, status_code, message=''):
        self.status_code = status_code
        self.message = message

    def to_dict(self):
        return {'status_code': self.status_code, 'message': self.message}


def auth_required(roles=None):
    if roles is None:
        roles = ['user']

    def decorator(func):
        # Attach auth info to the function object
        func._auth_roles = roles
        return func

    return decorator


async def check_permission(method, user_id, user_roles):
    if not hasattr(method, '_auth_roles'):
        return None

    required_roles = getattr(method, '_auth_roles')

    if not user_id:
        return AccessResponse(403, 'Forbidden')

    if not user_roles or not any(role in user_roles for role in required_roles):
        return AccessResponse(401, 'Unauthorized')

    return None
