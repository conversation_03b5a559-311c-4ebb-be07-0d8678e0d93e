import importlib

from fastapi import FastAP<PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from omni.api.auth import authenticate_user, check_permission
from omni.api.blueprint_register import register_routers
from omni.api.exception import MException
from omni.api.handler_register import handlers, load_handlers
from omni.log.log import olog

# 创建 FastAPI 应用
app = FastAPI()

# 启用跨域资源共享
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 启用响应压缩
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 加预定义路由
register_routers(app, 'omni.api.blueprint')
register_routers(app, 'blueprint')

# 加预定义API
api_modules = [
    'omni.api.api.asr_api',
    'omni.api.api.oss_api', 
    'omni.api.api.tts_api'
]

for module_name in api_modules:
    importlib.import_module(module_name)
    olog.info(f"注册API: {module_name}")

# 加载所有处理器
load_handlers()

@app.post("/api")
async def api(request: Request):
    try:
        # 获取请求头中的 Authorization 字段
        token = request.headers.get('Authorization')

        # 只能是 POST 请求, 直接获取JSON
        body = await request.json()
        resource = body.get('resource')
        method_name = body.get('method_name')
        handler_data = body.get('data', {})
        if handler_data is None:
            handler_data = {}

        # 获取对应的处理器
        handler = handlers.get(resource)
        if not handler:
            raise MException('无效资源路由')

            # 获取处理器中的方法
        method = getattr(handler, method_name, None)
        if not method:
            raise MException('无效方法路由')

            # 通过token认证并获取用户信息
        user_id, user_roles = await authenticate_user(token)

        # 用户鉴权
        permission_result = await check_permission(method, user_id, user_roles)
        if permission_result:
            if permission_result.status_code == 403:
                return JSONResponse(content={"code": 403, "message": "请先登录"})
            elif permission_result.status_code == 401:
                return JSONResponse(content={"code": 401, "message": "权限不足"})

        # 将用户信息添加到请求体中
        handler_data['user_id'] = user_id
        handler_data['user_roles'] = user_roles

        # 进行auth_required的权限验证+后续的逻辑处理
        result = await method(handler_data)

        # 使用原始的Response就直接返回
        if isinstance(result, Response):
            return result

        # 如果是 Pydantic 模型，则转换为字典
        if isinstance(result, BaseModel):
            result = result.model_dump()

        # 返回最终的 JSON 响应
        return JSONResponse(content={"code": 200, "message": "OK", "data": result})

    except MException as e:
        # 处理自定义异常
        olog.exception(f"报错资源：{resource}:{method_name}")
        return JSONResponse(content={"code": 500, "message": str(e)})
    except Exception as e:
        # 处理其他异常
        olog.exception(f"报错资源：{resource}:{method_name}")
        return JSONResponse(content={"code": 500, "message": "系统异常"})


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    # 全局异常处理
    olog.exception(f"全局系统错误：{type(exc)}，{exc}")
    return JSONResponse(content={"code": 500, "message": "系统异常"})
