import importlib
import os
from typing import Dict, Any, Type, Callable
from omni.log.log import olog

handlers: Dict[str, Any] = {}


def register_handler(handler_name: str) -> Callable[[Type], Type]:
    """注册处理器类"""
    def decorator(cls: Type) -> Type:
        handlers[handler_name] = cls()
        return cls
    return decorator


def load_handlers() -> None:
    """加载处理器模块"""
    handlers_dir = 'api'
    for root, _, files in os.walk(handlers_dir):
        for filename in files:
            if filename.endswith('_api.py'):
                file_path = os.path.join(root, filename)
                relative_path = os.path.relpath(file_path, handlers_dir)
                module_name = f"{handlers_dir}.{relative_path[:-3].replace(os.path.sep, '.')}"
                
                olog.info(f"注册API: {module_name}")
                importlib.import_module(module_name)