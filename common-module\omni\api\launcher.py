import multiprocessing
import subprocess
import time

import uvicorn

from models.models import User
from omni.config.config_loader import config_dict
from omni.mongo.mongo_client import init_models
from omni.scheduler.schedule_register import init_scheduler

# 配置变量
PROCESS_ENV = config_dict.get('process_env', 'dev')
SERVER_PORT = config_dict.get('server', {}).get('port', 5000)
SERVER_TIMEOUT = config_dict.get('server', {}).get('timeout', 30)
ADMIN_USERNAME = config_dict.get('admin_user', {}).get('username', 'admin')
ADMIN_PASSWORD = config_dict.get('admin_user', {}).get('password', 'y708sWekpvoRdIpF')


def _run_uvicorn(options):
    workers = (multiprocessing.cpu_count() * 2) + 1
    command = [
        "gunicorn",
        "omni.api.gateway:app",
        "-w",
        str(workers),
        "-k",
        "uvicorn.workers.UvicornWorker",
        "--bind",
        f"0.0.0.0:{options['port']}",
    ]
    subprocess.run(command)


async def _run_uvicorn_dev(options):
    config = uvicorn.Config(
        "omni.api.gateway:app",
        host="0.0.0.0",
        port=options["port"],
        log_level="debug"
    )
    server = uvicorn.Server(config)
    await server.serve()


async def _start_api_server():
    options = {
        'port': SERVER_PORT,
        'timeout': SERVER_TIMEOUT
    }

    if PROCESS_ENV == 'dev':
        await _run_uvicorn_dev(options)
    else:
        _run_uvicorn(options)


async def _init_admin_user():
    user = await User.find_one(User.username == ADMIN_USERNAME)
    if not user:
        user = User(
            username=ADMIN_USERNAME,
            password=ADMIN_PASSWORD,
            roles=['admin'],
            create_at=int(time.time()),
        )
        await user.insert()


async def start_api_server():
    await init_models()
    await _init_admin_user()
    await init_scheduler()
    await _start_api_server()
