import os
import sys

import yaml

from env import process_env

config_dict = {'process_env': process_env}


def _find_config_path():
    """根据sys.path查找config目录"""
    for path in sys.path:
        config_path = os.path.join(path, 'config')
        if os.path.exists(config_path) and os.path.isdir(config_path):
            return os.path.abspath(config_path)


config_base_path = _find_config_path()


def _load_yaml_file(filename):
    config_file = os.path.join(config_base_path, filename)
    if os.path.exists(config_file):
        with open(config_file, 'r') as file:
            env_data = yaml.safe_load(file)
            config_dict.update(env_data)


_load_yaml_file('settings.yaml')
_load_yaml_file(f'{process_env}.yaml')
