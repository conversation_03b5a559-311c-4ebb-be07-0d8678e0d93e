import asyncio
import functools
import inspect
import time
from typing import Callable, TypeVar, ParamSpec, Union
from omni.log.log import olog

P = ParamSpec('P')
T = TypeVar('T')


def retry_on_exception(max_retries: int = 3, exclude_exceptions: list = None):
    exclude_exceptions = exclude_exceptions or []
    
    # 内部默认重试延迟（秒）
    retry_delay = 1.0
    
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        def execute_with_retry(func_call: Callable[[], T], sleep_func: Callable[[float], None]) -> T:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    if attempt > 0:
                        olog.info(f"第 {attempt} 次重试执行函数 {func.__name__}")
                    
                    result = func_call()
                    
                    if attempt > 0:
                        olog.info(f"函数 {func.__name__} 重试成功")
                    
                    return result
                    
                except Exception as e:
                    if any(isinstance(e, exc_type) for exc_type in exclude_exceptions):
                        olog.debug(f"函数 {func.__name__} 遇到排除异常 {type(e).__name__}，不进行重试")
                        raise e
                    
                    last_exception = e
                    
                    if attempt < max_retries:
                        olog.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {str(e)}")
                        sleep_func(retry_delay)
                    else:
                        olog.error(f"函数 {func.__name__} 所有重试尝试均失败，最后一次错误: {str(e)}")
            
            raise last_exception
        
        @functools.wraps(func)
        async def async_wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    if attempt > 0:
                        olog.info(f"第 {attempt} 次重试执行函数 {func.__name__}")
                    
                    result = await func(*args, **kwargs)
                    
                    if attempt > 0:
                        olog.info(f"函数 {func.__name__} 重试成功")
                    
                    return result
                    
                except Exception as e:
                    if any(isinstance(e, exc_type) for exc_type in exclude_exceptions):
                        olog.debug(f"函数 {func.__name__} 遇到排除异常 {type(e).__name__}，不进行重试")
                        raise e
                    
                    last_exception = e
                    
                    if attempt < max_retries:
                        olog.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {str(e)}")
                        await asyncio.sleep(retry_delay)
                    else:
                        olog.error(f"函数 {func.__name__} 所有重试尝试均失败，最后一次错误: {str(e)}")
            
            raise last_exception
        
        @functools.wraps(func)
        def sync_wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            return execute_with_retry(
                lambda: func(*args, **kwargs),
                time.sleep
            )
        
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator