"""
smtp_163:
  sender_email: <EMAIL>
  authorization_code: NTELLLKPEGQBMJHZ
"""
import asyncio
import smtplib
from email.mime.text import MIMEText

from omni.config.config_loader import config_dict


class Email163Sender:
    def __init__(self):
        self.sender_email = config_dict['smtp_163']['sender_email']
        self.authorization_code = config_dict['smtp_163']['authorization_code']
        self.smtp_server = "smtp.163.com"
        self.smtp_port = 25

    def _send_email_sync(self, receiver_email, subject, body):
        # 创建MIMEText对象
        msg = MIMEText(body, 'plain', 'utf-8')
        msg['From'] = self.sender_email
        msg['To'] = receiver_email
        msg['Subject'] = subject

        # 连接到SMTP服务器并发送邮件
        try:
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.sender_email, self.authorization_code)
                server.send_message(msg)
            print("邮件发送成功!")
        except Exception as e:
            print(f"邮件发送失败: {str(e)}")

    async def send_email(self, receiver_email, subject, body):
        await asyncio.to_thread(self._send_email_sync, receiver_email, subject, body)
