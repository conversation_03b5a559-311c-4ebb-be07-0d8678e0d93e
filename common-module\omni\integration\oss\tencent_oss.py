"""
oss:
  region: ap-guangzhou
  secret_id: AKIDPiyNkx0wlV8Yr14ylWRJJIhzAEAtUxKb
  secret_key: HQfojQsCKEzRoyuc6MQGsHVhtAHBxYvJ
  bucket_name: projects-1323741919
"""
import asyncio
import os
import random
import string
from datetime import datetime
from pathlib import Path

from qcloud_cos import CosConfig, CosS3Client

from omni.config.config_loader import config_dict
from omni.log.log import olog


class SignedMethod:
    PUT = "PUT"
    POST = "POST"
    GET = "GET"
    DELETE = "DELETE"
    HEAD = "HEAD"


class OSSClient:
    def __init__(self):
        self._initialize()

    def _initialize(self):
        oss_config = config_dict.get("oss", {})
        secret_id = oss_config.get("secret_id")
        secret_key = oss_config.get("secret_key")
        region = oss_config.get("region")
        self.bucket_name = oss_config.get("bucket_name")

        if not all([secret_id, secret_key, region, self.bucket_name]):
            olog.warning("OSS配置不完整，部分OSS功能可能无法正常使用。")
            self.client = None
            return

        config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key)
        self.client = CosS3Client(config)

    def _check_client(self):
        if not self.client:
            olog.error("OSS 客户端因缺少配置而未初始化。")
            return False
        return True

    def get_client(self):
        """
        暴露内部的 CosS3Client 客户端实例。

        :return: CosS3Client 实例
        """
        if not self._check_client():
            return None
        return self.client

    async def signed_url(self, method, key):
        if not self._check_client():
            return None
        
        if not method:
            raise ValueError("method 参数不能为空")
        if not key:
            raise ValueError("key 参数不能为空")
            
        return await asyncio.to_thread(
            self.client.get_presigned_url,
            Bucket=self.bucket_name, Key=key, Method=method, Expired=604800
        )

    async def signed_get_url(self, key):
        return await self.signed_url(SignedMethod.GET, key)

    async def signed_put_url(self, key):
        return await self.signed_url(SignedMethod.PUT, key)

    async def signed_post_url(self, key):
        return await self.signed_url(SignedMethod.POST, key)

    async def signed_delete_url(self, key):
        return await self.signed_url(SignedMethod.DELETE, key)

    async def signed_head_url(self, key):
        return await self.signed_url(SignedMethod.HEAD, key)

    async def upload_file(self, key, local_file_path):
        if not self._check_client():
            return None
        # 上传文件
        await asyncio.to_thread(
            self.client.upload_file,
            Bucket=self.bucket_name,
            LocalFilePath=local_file_path,
            Key=key,
            PartSize=1,
            MAXThread=10,
            EnableMD5=False,
        )
        olog.info(f"文件上传成功，路径为: {key}")
        return key

    async def download_file(self, key, local_file_dir="tmp"):
        if not self._check_client():
            return None
        """
        从 OSS 下载文件到本地目录。

        :param key: 文件在 OSS 中的 key
        :param local_file_dir: 本地存储目录，默认为 "tmp"
        :return: 下载后的本地文件路径
        """
        # 确保目录存在
        Path(local_file_dir).mkdir(parents=True, exist_ok=True)

        # 生成随机文件名
        file_extension = os.path.splitext(key)[1]
        time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        rand_part = "".join(random.choices(string.ascii_letters + string.digits, k=6))
        local_file_path = f"{local_file_dir}/{time_str}_{rand_part}{file_extension}"

        # 下载文件
        response = await asyncio.to_thread(self.client.get_object, Bucket=self.bucket_name, Key=key)
        await asyncio.to_thread(response["Body"].get_stream_to_file, local_file_path)
        olog.info(f"文件下载成功，路径为: {local_file_path}")
        return local_file_path

    async def upload_bytes(self, parent_key: str, file_extension: str, file_bytes: bytes) -> str:
        if not self._check_client():
            return None
        """
        将原始的 bytes 数据上传到 OSS，通过临时文件作为中转。

        :param parent_key: OSS中的父路径（类似目录）
        :param file_extension: 文件的扩展名 (例如: '.png', '.jpg')，确保包含 '.'
        :param file_bytes: 原始的文件内容 bytes
        :return: 上传成功后的完整文件 key
        """
        if not file_extension.startswith("."):
            file_extension = "." + file_extension

        temp_dir = Path("tmp")
        temp_dir.mkdir(parents=True, exist_ok=True)
        temp_filename = f"temp_{datetime.now().strftime('%Y%m%d%H%M%S')}_{''.join(random.choices(string.ascii_letters + string.digits, k=6))}{file_extension}"
        temp_file_path = temp_dir / temp_filename

        try:
            with open(temp_file_path, "wb") as f:
                f.write(file_bytes)

            time_str = datetime.now().strftime("%Y%m%d%H%M%S")
            rand_part = "".join(random.choices(string.ascii_letters + string.digits, k=6))
            key = f"{parent_key}/{time_str}_{rand_part}{file_extension}"

            await self.upload_file(key, str(temp_file_path))
            return key
        finally:
            if temp_file_path.exists():
                os.remove(temp_file_path)

    async def download_bytes(self, key: str) -> bytes:
        if not self._check_client():
            return None
        """
        根据 key 从 OSS 下载文件，并返回原始的 bytes 数据。

        :param key: 文件在 OSS 中的 key
        :return: 原始的文件内容 bytes
        """
        temp_file_path = await self.download_file(key, "tmp")
        try:
            with open(temp_file_path, "rb") as f:
                return f.read()
        finally:
            if temp_file_path and Path(temp_file_path).exists():
                os.remove(temp_file_path)


oss_client = OSSClient()
