# -*- coding: utf-8 -*-
"""
tts:
  region: ap-guangzhou
  secret_id: AKID4A8vX0IPK6iC91lKiuwpX4RdeUBh2Sr5
  secret_key: BA3scHsJYcUDm8NSL38DC3nQnBTenskP
callback:
  tts_callback_url: https://xxx/tencent-tts-callback
"""
import asyncio
import json
import time
import traceback

from tencentcloud.common import credential
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.tts.v20190823 import tts_client, models

from omni.config.config_loader import config_dict
from omni.log.log import olog
from omni.redis.redis_client import rc


class TencentTTS:
    _instance = None

    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = False

    @classmethod
    def getInstance(cls):
        if cls._instance is None:
            cls._instance = cls()
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        if self._initialized:
            return

        region = config_dict['tts']['region']
        secret_id = config_dict['tts']['secret_id']
        secret_key = config_dict['tts']['secret_key']
        self.cred = credential.Credential(secret_id, secret_key)

        self.httpProfile = HttpProfile()
        self.httpProfile.endpoint = "tts.tencentcloudapi.com"

        self.clientProfile = ClientProfile()
        self.clientProfile.httpProfile = self.httpProfile

        self.client = tts_client.TtsClient(self.cred, region, self.clientProfile)

        self._initialized = True

    async def long_text_to_voice(self, text, voice_type=301033, max_wait_time=180):
        if not self._initialized:
            raise RuntimeError("TencentTTS is not initialized. Call getInstance first.")

        try:
            req = models.CreateTtsTaskRequest()
            params = {
                "Text": text,
                "VoiceType": voice_type,
                "CallbackUrl": config_dict['callback']['tts_callback_url']
            }
            req.from_json_string(json.dumps(params))

            resp = self.client.CreateTtsTask(req)
            task_id = resp.Data.TaskId

            # 添加轮询逻辑
            start_time = time.time()
            while True:
                if time.time() - start_time > max_wait_time:
                    return {'status': 'timeout', 'error_msg': '转换超时，请稍后重试', 'url': None}

                result_data = await rc.get(f"tts:{task_id}")
                if result_data:
                    try:
                        result = json.loads(result_data)
                        if result['status'] == 'success':
                            return {'status': 'success', 'url': result['url'], 'error_msg': None}
                        elif result['status'] == 'failed':
                            return {'status': 'failed', 'error_msg': result['error_msg'], 'url': None}
                    except json.JSONDecodeError:
                        return {'status': 'error', 'error_msg': '数据解析错误', 'url': None}

                await asyncio.sleep(0.1)

        except TencentCloudSDKException as err:
            olog.error(f"An error occurred in long text TTS: {traceback.format_exc()}")
            return {'status': 'failed', 'error_msg': str(err), 'url': None}
