from typing import Optional, Dict

from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel

from omni.config.config_loader import config_dict

_default_llm_name: Optional[str] = None
_llm_instances: Dict[str, BaseChatModel] = {}


def _load_llm_configs_and_instances() -> None:
    global _default_llm_name, _llm_instances
    llm_config = config_dict.get("llm", {})
    _default_llm_name = llm_config.get("DEFAULT-LLM-NAME")

    llm_configs = {k: v for k, v in llm_config.items()
                   if k != "DEFAULT-LLM-NAME" and isinstance(v, dict)}

    for llm_name, config in llm_configs.items():
        api_base = config.get("api_base")
        model_name = config.get("model_name")
        api_key = config.get("api_key")

        _llm_instances[llm_name] = init_chat_model(
            model=model_name,
            model_provider="openai",
            base_url=api_base,
            api_key=api_key
        )


_load_llm_configs_and_instances()


def get_llm(llm_name: Optional[str] = None) -> BaseChatModel:
    if llm_name is None:
        llm_name = _default_llm_name
    return _llm_instances[llm_name]
