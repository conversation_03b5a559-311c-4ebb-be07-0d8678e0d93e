"""
local_dir_path:
  log: /workspace/aqua/log
"""
import asyncio
import logging
import socket
from pathlib import Path

import colorlog

from omni.config.config_loader import config_dict


class AsyncioFormatter(logging.Formatter):
    """支持协程任务名称的日志格式化器"""
    def format(self, record):
        try:
            task = asyncio.current_task()
            if task:
                record.taskName = task.get_name()
            else:
                record.taskName = "NoTask"
        except RuntimeError:
            record.taskName = "NoAsyncio"
        return super().format(record)


def setup_logging():
    # 从配置中获取日志路径和环境
    folder_path = config_dict['local_dir_path']['log']
    process_env = config_dict['process_env']
    hostname = socket.gethostname()

    # 创建日志目录（如果不存在）
    Path(folder_path).mkdir(parents=True, exist_ok=True)

    # 定义文件日志格式
    file_formatter = AsyncioFormatter(
        f"%(asctime)s | {hostname} | %(threadName)s | %(taskName)s | %(funcName)s | %(levelname)s | %(message)s",
        datefmt='%Y-%m-%d/%H:%M:%S'
    )

    # 获取或创建名为 'mlog' 的日志器
    logger = logging.getLogger('mlog')
    logger.setLevel(logging.DEBUG)

    # 设置控制台处理器
    console_formatter = colorlog.ColoredFormatter(
        f"%(log_color)s%(asctime)s | {hostname} | %(threadName)s | %(funcName)s | %(levelname)s | %(message)s%(reset)s",
        datefmt='%Y-%m-%d/%H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        },
        secondary_log_colors={},
        style='%'
    )

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(logging.DEBUG)

    logger.addHandler(console_handler)

    # 设置文件处理器
    log_levels = {
        logging.DEBUG: 'debug_log.txt',
        logging.ERROR: 'error_log.txt',
    }

    for level, filename in log_levels.items():
        file_handler = logging.FileHandler(f'{folder_path}/{filename}', encoding='utf-8')
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(level)
        logger.addHandler(file_handler)

    return logger


# 导出日志器实例
olog = setup_logging()
