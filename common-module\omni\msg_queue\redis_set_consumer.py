"""Redis Set 消费者模块"""
import asyncio
import importlib
import json
import pkgutil
from typing import Callable, Optional, List

from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.redis.redis_client import rc

_registered_consumers = []


async def _run_consumer_task(redis_key: str, processing_func: Callable, task_id: int) -> None:
    """运行单个消费者任务"""
    task_name = f"RedisSetConsumer-{redis_key}-{task_id}"

    while True:
        try:
            raw_message = await rc.spop(redis_key)
            if not raw_message:
                await asyncio.sleep(0.1)
                continue

            # 解析消息
            try:
                # 如果是字节类型，先解码为字符串
                if isinstance(raw_message, bytes):
                    message_str = raw_message.decode('utf-8')
                else:
                    message_str = raw_message

                # 尝试解析为 JSON
                message = json.loads(message_str)
                await processing_func(message)
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                # 如果解析失败，记录警告并跳过
                olog.warning(f"任务 {task_name} 消息解析失败，跳过处理: {e}, 原始消息: {raw_message}")
                continue

        except Exception as e:
            olog.exception(f"任务 {task_name} 处理消息时出错: {e}")
            await asyncio.sleep(1.0)


def consume_redis_set(redis_key: str, num_tasks: int = 1) -> Callable:
    """装饰器：注册函数为Redis Set消费者"""

    def decorator(func: Callable) -> Callable:
        _registered_consumers.append((func, redis_key, num_tasks))
        return func

    return decorator


def _discover_consumers(package_paths: List[str]) -> None:
    """发现指定包路径下的所有消费者模块"""
    for package_path in package_paths:
        try:
            package = importlib.import_module(package_path)
            if hasattr(package, '__path__'):
                for _, name, is_pkg in pkgutil.walk_packages(package.__path__, prefix=package_path + '.'):
                    if not is_pkg:
                        try:
                            importlib.import_module(name)
                        except ImportError as e:
                            olog.warning(f"导入模块 {name} 时出错: {e}")
        except ImportError as e:
            olog.error(f"无法导入包路径 {package_path}: {e}")


async def start_all_consumers(package_paths: Optional[List[str]] = None) -> None:
    """发现并启动所有已注册的消费者任务"""
    package_paths = package_paths or ['consumers']
    _discover_consumers(package_paths)

    if not _registered_consumers:
        olog.warning("没有找到已注册的 Redis Set 消费者")
        return

    olog.info(f"共发现 {len(_registered_consumers)} 个消费者配置，正在启动")
    task_count = 0

    for consumer_func, redis_key, num_tasks in _registered_consumers:
        for i in range(num_tasks):
            task_id = i + 1
            asyncio.create_task(
                _run_consumer_task(redis_key, consumer_func, task_id),
                name=f"RedisSetConsumer-{redis_key}-{task_id}"
            )
            task_count += 1
            olog.info(
                f"启动消费者: Key='{redis_key}', Handler='{consumer_func.__name__}', TaskNum={task_id}/{num_tasks}")

    olog.info(f"消费者启动流程结束，成功启动 {task_count} 个任务")


async def start_consumer_server(package_paths: Optional[List[str]] = None) -> None:
    """启动所有消费者和初始化模型"""
    try:
        # 初始化数据库模型
        await init_models()

        # 启动所有消费者
        await start_all_consumers(package_paths)

        olog.info("消费者服务启动完成")

        # 保持程序运行
        while True:
            await asyncio.sleep(1)

    except Exception as e:
        olog.exception(f"启动服务时出错: {e}")
        raise
