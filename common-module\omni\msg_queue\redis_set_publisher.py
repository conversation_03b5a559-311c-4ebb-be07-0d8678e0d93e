"""
Redis Set 发布者模块

提供向 Redis Set 中添加消息的功能。
"""
import json

from omni.log.log import olog
from omni.redis.redis_client import rc


async def publish_messages_to_redis_set(redis_key: str, messages: dict | list[dict]) -> int:
    """
    向指定的 Redis Set 中添加消息，支持单个字典消息或多个字典消息。
    消息会被自动转换为JSON字符串后存储到Redis。

    Args:
        redis_key: 目标 Redis Set 的键名。
        messages: 要添加到 Set 中的消息，只能是字典或字典列表。
                 当传入 dict 时，添加单条消息；
                 当传入 list[dict] 时，批量添加多条消息。

    Returns:
        int: 成功添加到 Set 的新元素数量。
    
    Raises:
        TypeError: 当传入的消息不是字典或字典列表时抛出。
    """
    # 类型检查
    if not isinstance(messages, (dict, list)):
        raise TypeError("messages 必须是 dict 或 list[dict] 类型")

    if isinstance(messages, list):
        for i, msg in enumerate(messages):
            if not isinstance(msg, dict):
                raise TypeError(f"messages[{i}] 必须是 dict 类型，实际类型: {type(msg)}")

    try:
        # 处理字典列表 - 批量添加
        if isinstance(messages, list):
            if not messages:
                olog.debug(f"向 Redis Set '{redis_key}' 添加的消息列表为空")
                return 0

            # 将列表中的每个消息转换为JSON字符串
            json_messages = [json.dumps(msg, ensure_ascii=False) for msg in messages]
            num_added = await rc.sadd(redis_key, *json_messages)
            olog.debug(f"向 Redis Set '{redis_key}' 批量添加 {len(messages)} 条消息，成功添加 {num_added} 条新消息")
            return num_added

        # 处理单个字典消息
        json_message = json.dumps(messages, ensure_ascii=False)
        result = await rc.sadd(redis_key, json_message)
        olog.debug(f"向 Redis Set '{redis_key}' 添加单条消息{'成功' if result == 1 else '失败(消息已存在)'}")
        return result

    except (TypeError, ValueError) as e:
        olog.error(f"消息序列化为JSON失败: {e}")
        raise ValueError(f"消息无法序列化为JSON: {e}")
