import asyncio
import random
import uuid

from omni.log.log import olog
from omni.redis.redis_client import rc


class MachineSemaphoreLock:
    """基于物理机的Redis信号量锁，控制同一台物理机上的并发数量
    
    使用方式：
    ```python
    # 基本用法（默认并发数为1）
    async with MachineSemaphoreLock() as lock:
        # 你的代码
        pass
    
    # 自定义并发数
    async with MachineSemaphoreLock(max_concurrent=2) as lock:
        # 你的代码
        pass
    
    # 自定义参数
    async with MachineSemaphoreLock(max_concurrent=3, timeout=600, lock_name="resource") as lock:
        # 你的代码
        pass
    ```
    
    Docker配置：需要挂载 `-v /etc/machine-id:/host/machine-id:ro`
    """

    # Lua脚本：原子性获取信号量锁
    ACQUIRE_SCRIPT = """
    local semaphore_key = KEYS[1]
    local instance_key = KEYS[2]
    local max_concurrent = tonumber(ARGV[1])
    local timeout = tonumber(ARGV[2])
    
    -- 参数有效性检查
    if not max_concurrent or max_concurrent <= 0 then
        return {0, -1}
    end
    if not timeout or timeout <= 0 then
        timeout = 300
    end
    
    local count = redis.call('INCR', semaphore_key)
    
    if count <= max_concurrent then
        redis.call('EXPIRE', semaphore_key, timeout)
        redis.call('SETEX', instance_key, timeout, 'acquired')
        return {1, count}
    else
        redis.call('DECR', semaphore_key)
        return {0, count - 1}
    end
    """

    # Lua脚本：原子性释放信号量锁
    RELEASE_SCRIPT = """
    local semaphore_key = KEYS[1]
    local instance_key = KEYS[2]
    
    -- 检查实例锁是否存在
    local instance_exists = redis.call('EXISTS', instance_key)
    if instance_exists == 0 then
        return {0, -1}  -- 实例锁不存在
    end
    
    -- 删除实例锁并减少计数
    redis.call('DEL', instance_key)
    
    -- 检查信号量键是否存在，避免负数
    local current_count = redis.call('GET', semaphore_key)
    if not current_count then
        -- 信号量键不存在，可能已经被清理
        return {1, 0}
    end
    
    local count = redis.call('DECR', semaphore_key)
    
    -- 如果计数归零或负数，删除信号量键并返回0
    if count <= 0 then
        redis.call('DEL', semaphore_key)
        count = 0
    end
    
    return {1, count}
    """

    # Lua脚本：强制清理异常情况下的锁（原子性操作）
    FORCE_CLEANUP_SCRIPT = """
    local semaphore_key = KEYS[1]
    local instance_key = KEYS[2]
    
    -- 检查实例锁是否存在
    local instance_exists = redis.call('EXISTS', instance_key)
    if instance_exists == 0 then
        return {0, 0}  -- 实例锁不存在，无需清理
    end
    
    -- 删除实例锁
    redis.call('DEL', instance_key)
    
    -- 检查并更新信号量计数
    local current_count = redis.call('GET', semaphore_key)
    if current_count and tonumber(current_count) > 0 then
        local new_count = redis.call('DECR', semaphore_key)
        -- 如果计数归零或负数，删除信号量键
        if new_count <= 0 then
            redis.call('DEL', semaphore_key)
            new_count = 0
        end
        return {1, new_count}
    else
        -- 信号量键不存在或已经为0
        return {1, 0}
    end
    """

    def __init__(self, max_concurrent: int = 1, timeout: int = 300, lock_name: str = "playwright"):
        """初始化机器信号量锁
        
        Args:
            max_concurrent: 最大并发数（默认为1）
            timeout: 锁超时时间（秒，默认为300）
            lock_name: 锁名称，用于区分不同类型的锁（默认为"playwright"）
        """
        if max_concurrent <= 0:
            raise ValueError("max_concurrent必须大于0")

        self.max_concurrent = max_concurrent
        self.timeout = timeout
        self.lock_name = lock_name
        self.machine_id = self._get_machine_id()
        self.instance_id = str(uuid.uuid4())

        # Redis键名
        self.semaphore_key = f"semaphore:{lock_name}:{self.machine_id}"
        self.instance_key = f"instance:{lock_name}:{self.machine_id}:{self.instance_id}"

        olog.debug(f"初始化MachineSemaphoreLock: machine_id={self.machine_id}, max_concurrent={max_concurrent}, "
                   f"timeout={timeout}, lock_name={lock_name}")
        olog.debug(f"Redis键名: semaphore_key={self.semaphore_key}, instance_key={self.instance_key}")

    def _get_machine_id(self) -> str:
        """获取机器ID
        
        从/host/machine-id读取，失败时返回默认值localhost
        
        Returns:
            str: 机器唯一标识，读取失败时返回'localhost'
        """
        try:
            with open('/host/machine-id', 'r') as f:
                machine_id = f.read().strip()
                if machine_id:
                    return machine_id
                else:
                    olog.warning("机器ID文件为空，使用默认值 localhost")
                    return "localhost"
        except (FileNotFoundError, PermissionError, IOError) as e:
            olog.warning(f"无法读取/host/machine-id: {e}。请确保Docker挂载了机器ID文件，使用默认值 localhost")
            return "localhost"

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._acquire()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._release()

    async def _acquire(self):
        """获取信号量锁"""
        if not rc:
            olog.warning("Redis客户端未初始化，跳过锁获取")
            return

        olog.debug(f"开始获取信号量锁: {self.semaphore_key}, 最大并发数: {self.max_concurrent}")

        error_count = 0
        max_errors = 20  # 最大连续错误次数
        attempt_count = 0

        while True:
            attempt_count += 1

            # 检查连续错误次数
            if error_count >= max_errors:
                olog.error(f"连续错误次数过多: {error_count}次, 总尝试次数: {attempt_count}")
                raise RuntimeError(f"获取信号量锁失败，连续错误次数超过{max_errors}次")

            # 定期记录尝试状态
            if attempt_count % 10 == 1 or attempt_count <= 5:
                olog.debug(f"第{attempt_count}次尝试获取锁, 错误次数{error_count}")

            try:
                # 使用eval方式调用Lua脚本
                result = await rc.eval(
                    self.ACQUIRE_SCRIPT,
                    2,
                    self.semaphore_key, self.instance_key,
                    self.max_concurrent, self.timeout
                )

                success, count = result
                if success:
                    olog.info(f"成功获取信号量锁: {self.semaphore_key}, 当前计数: {count}/{self.max_concurrent}, "
                              f"总尝试次数: {attempt_count}")
                    return
                else:
                    # 重置错误计数（正常响应）
                    error_count = 0
                    if attempt_count % 5 == 0:  # 每5次尝试记录一次
                        olog.debug(f"信号量锁已满，继续等待: {count}/{self.max_concurrent}, "
                                   f"已尝试{attempt_count}次")
                    # 添加随机延迟，避免所有协程同时重试
                    wait_time = 0.1 + random.uniform(0, 0.1)
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_count += 1
                # 检查是否为致命错误，如果是则立即停止重试
                if self._is_fatal_error(e):
                    olog.error(f"遇到致命错误，停止重试: {e}")
                    raise

                olog.error(f"获取信号量锁时发生错误 (第{error_count}次, 总尝试{attempt_count}次): "
                           f"{type(e).__name__}: {e}")

                # 使用指数退避策略，但有上限
                base_wait = 0.1
                backoff_wait = min(base_wait * (2 ** min(error_count - 1, 5)), 2.0)
                wait_time = backoff_wait + random.uniform(0, 0.1)
                olog.debug(f"错误后等待 {wait_time:.2f}秒 (退避等待: {backoff_wait:.2f}秒)")
                await asyncio.sleep(wait_time)

    def _is_fatal_error(self, error: Exception) -> bool:
        """判断是否为致命错误，应该立即停止重试
        
        Args:
            error: 异常对象
            
        Returns:
            bool: True表示致命错误，应该停止重试
        """
        # 连接相关的错误通常是可恢复的，但某些认证或配置错误是致命的
        error_str = str(error).lower()

        # 致命错误模式
        fatal_patterns = [
            'authentication',  # 认证失败
            'permission denied',  # 权限错误
            'access denied',  # 访问被拒绝
            'connection refused',  # 连接被拒绝（可能是服务未启动）
            'invalid argument',  # 参数错误
            'lua script error',  # Lua脚本错误
        ]

        return any(pattern in error_str for pattern in fatal_patterns)

    async def _release(self):
        """释放信号量锁"""
        if not rc:
            olog.warning("Redis客户端未初始化，跳过锁释放")
            return

        olog.debug(f"开始释放信号量锁: {self.semaphore_key}")

        try:
            # 使用eval方式调用Lua脚本
            result = await rc.eval(
                self.RELEASE_SCRIPT,
                2,
                self.semaphore_key, self.instance_key
            )

            success, count = result
            if success:
                olog.info(f"成功释放信号量锁: {self.semaphore_key}, 当前计数: {count}/{self.max_concurrent}")
            else:
                olog.warning(f"实例锁不存在或已过期: {self.instance_key}")

        except Exception as e:
            olog.error(f"释放信号量锁时发生错误: {e}")
            # 如果发生异常，尝试使用原子性Lua脚本强制清理
            await self._force_cleanup_with_script()

    async def _force_cleanup_with_script(self):
        """使用Lua脚本原子性地执行强制清理"""
        if not rc:
            return

        try:
            # 使用eval方式调用Lua脚本
            result = await rc.eval(
                self.FORCE_CLEANUP_SCRIPT,
                2,
                self.semaphore_key, self.instance_key
            )

            success, count = result
            if success:
                olog.info(f"异常情况下强制清理成功: {self.semaphore_key}, 当前计数: {count}/{self.max_concurrent}")
            else:
                olog.warning("实例锁不存在，无需清理")

        except Exception as cleanup_e:
            olog.error(f"强制清理失败: {cleanup_e}")

    async def get_current_count(self) -> int:
        """获取当前信号量计数
        
        Returns:
            int: 当前使用的锁数量
        """
        if not rc:
            return 0

        try:
            count = await rc.get(self.semaphore_key)
            return int(count) if count else 0
        except Exception as e:
            olog.error(f"获取信号量计数时发生错误: {e}")
            return 0

    async def force_cleanup(self):
        """强制清理所有相关的锁（慎用）"""
        if not rc:
            return

        try:
            # 清理信号量和所有实例锁
            pattern = f"instance:{self.lock_name}:{self.machine_id}:*"
            keys = await rc.keys(pattern)
            if keys:
                await rc.delete(*keys)
            await rc.delete(self.semaphore_key)
            olog.info(f"强制清理完成: {self.semaphore_key}")
        except Exception as e:
            olog.error(f"强制清理时发生错误: {e}")
