import redis.asyncio as redis

from omni.config.config_loader import config_dict
from omni.log.log import olog


def _create_redis_client():
    """
    根据配置创建并返回一个Redis客户端实例。
    如果未配置Redis URL，则记录警告并返回None。
    """
    redis_url = config_dict.get("redis", {}).get("url")
    if not redis_url:
        olog.warning("未配置 Redis URL, RedisClient 将返回 None")
        return None
    redis_client = redis.Redis.from_url(redis_url, health_check_interval=30, socket_keepalive=True,
                                        decode_responses=True)
    olog.info("Redis客户端初始化完成")
    return redis_client


# 在模块加载时创建Redis客户端单例
rc = _create_redis_client()
