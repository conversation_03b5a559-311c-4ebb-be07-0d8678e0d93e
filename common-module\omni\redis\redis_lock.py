import asyncio

from omni.redis.redis_client import rc


# 阻塞锁
async def acquire_block_lock(lock_name, lock_timeout=60):
    while True:
        if await acquire_lock(lock_name, lock_timeout):
            return
        else:
            await asyncio.sleep(0.1)


# 获得锁,超时后自动释放,timeout秒为单位
async def acquire_lock(lock_name, lock_timeout=60):
    lock_key = f"lock:{lock_name}"
    if await rc.setnx(lock_key, "locked"):
        rc.expire(lock_key, lock_timeout)
        return True
    else:
        return False


async def release_lock(lock_name):
    lock_key = f"lock:{lock_name}"
    await rc.delete(lock_key)
