import abc

from omni.log.log import olog


class BaseScheduler(abc.ABC):
    @abc.abstractmethod
    async def run_task(self):
        pass

    def async_start(self, scheduler, **trigger_args):
        async def job_wrapper():
            try:
                await self.run_task()
            except Exception as e:
                olog.error(f"调度器 {self.__class__.__name__} 任务执行失败: {e}", exc_info=True)

        scheduler.add_job(func=job_wrapper, **trigger_args)
