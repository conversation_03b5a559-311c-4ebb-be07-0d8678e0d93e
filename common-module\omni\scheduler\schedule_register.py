"""
#添加在scheduler包中且以scheduler.py结尾的文件
@register_scheduler(trigger='interval', seconds=1)
class DemoScheduler(BaseScheduler):
    def run_task(self):
        syslog.info('run demo')
"""
import asyncio
import importlib
import os

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from pytz import timezone

from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler

scheduler = AsyncIOScheduler(timezone=timezone('Asia/Shanghai'))


# 装饰器函数，用于注册类
def register_scheduler(**trigger_args):
    def decorator(cls):
        enabled = trigger_args.pop('enabled', True)  # 默认启用
        if enabled and issubclass(cls, BaseScheduler) and cls is not BaseScheduler:
            olog.info(f"调度器 {cls.__name__} 注册成功")
            cls().async_start(scheduler, **trigger_args)
        elif not enabled:
            olog.info(f"调度器 {cls.__name__} 已禁用.")  # 修改为中文日志
        return cls

    return decorator


# 加载处理器模块
async def load_schedulers():
    schedulers_dir = 'scheduler'  # 处理器模块所在的目录
    if not os.path.exists(schedulers_dir):  # worker不存在就不注册
        return
    # 使用 os.walk 递归遍历所有子目录
    for root, dirs, files in os.walk(schedulers_dir):
        for filename in files:
            if filename.endswith('scheduler.py'):  # 只处理以'scheduler.py'结尾的文件
                # 构建相对路径并转换为模块名称
                relative_path = os.path.relpath(os.path.join(root, filename), schedulers_dir)
                module_name = f"{schedulers_dir.replace('/', '.')}.{relative_path.replace('/', '.').replace(os.sep, '.')[:-3]}"
                importlib.import_module(module_name)  # 动态导入模块


async def init_scheduler():
    await load_schedulers()
    scheduler.start()


async def start_scheduler_server():
    """异步启动调度器服务器"""
    await init_models()
    await init_scheduler()
    olog.info("调度器服务器已启动")
    try:
        # 保持调度器运行
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        olog.info("接收到停止信号，正在关闭调度器...")
        scheduler.shutdown()
        olog.info("调度器已关闭")
