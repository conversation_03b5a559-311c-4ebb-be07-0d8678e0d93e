# 前端 API 调用组件示例

## 一、API 调用参数与约定说明

- **resource** 与 **method_name** 是每次 API 调用时必须传递的参数。
  - `resource`：需与后端 Python 代码中 `@register_handler('xxx')` 装饰器的 `xxx` 保持一致，用于指定具体的资源类型或模块。
  - `method_name`：需与后端 Python 类中的方法名一致，用于指定要调用的具体方法。
- **user_id** 参数由后端根据认证信息自动获取，前端无需手动传递。
- **response** 即为后端 API 方法中 `return` 返回的内容，前端可直接获取和使用。
- 如需传递 id 参数，请统一使用 `_id` 作为参数名，避免直接使用 `id` 字段，以防与系统保留字段冲突，提升代码健壮性和可读性。

---

## 二、调用示例

```js
import api from "@/core/api/api";

const RESOURCE = "user_api";

export const userApi = {
  login: async (username, password) => {
    return await api({
      resource: RESOURCE,
      method_name: "login",
      data: {
        username,
        password,
      },
    });
  },
};
```