# 前端开发规范

## 一、风格约束

### 设计要求

- **UI和UX风格**：完全遵循Google Material Design设计语言，采用现代化的科技感设计风格，整体视觉和交互体验符合Google生态标准。

### 设计核心原则

- **质感设计（Material Design）**：运用纸质隐喻，通过阴影、层次、动效营造真实感。
- **无障碍友好**：遵循WCAG标准，确保可访问性。

### 视觉与交互细节

- 现代化、简洁、注重功能性
- 统一的圆角设计（0.75rem基础圆角）
- Google科技配色体系（已在globals.css中定义）
- 流畅的过渡动效和交互反馈

### 参考标准

- 请参考Google Material Design官方指南，确保产品在美观性、功能性和一致性上达到Google官方标准。

---

## 二、技术约束

### 核心技术栈
- **项目基础**：shadcn/ui + Tailwind CSS + Next.js 15 + TypeScript
- **客户端渲染**：默认采用客户端渲染，需在文件开头添加 `"use client"` 指令

### UI组件库
- **主要组件库**：shadcn/ui

### 扩展功能库

#### 图标与视觉
- **额外图标库**：@tabler/icons-react（提供更丰富的图标选择）
- **图表库**：recharts（用于数据可视化和图表展示）

#### 表单与验证
- **表单处理**：react-hook-form（用于表单状态管理和验证）
- **表单解析器**：@hookform/resolvers（react-hook-form验证集成）
- **数据验证**：zod（TypeScript优先的数据验证库，配合表单使用）

#### 日期与时间
- **日期处理**：date-fns（用于日期格式化和计算）
- **日期选择器**：react-day-picker（用于日期输入）

#### 交互功能
- **拖拽功能**：@dnd-kit系列（用于实现拖拽交互）
- **通知系统**：sonner（轻量级通知组件库，用于消息提醒）
- **主题切换**：next-themes（Next.js主题切换库，用于深色/浅色模式）

#### 布局与导航
- **表格组件**：@tanstack/react-table（功能强大的表格组件库，用于复杂数据表格）
- **轮播图**：embla-carousel-react（用于图片轮播展示）
- **面板组件**：react-resizable-panels（可调整大小的面板组件，用于布局分割）

### 路径引入规范
- 所有路径引入需要使用相对路径或绝对路径
- 组件导入优先使用 `@/` 别名（如import XXX from '@/components/ui/badge';）

---