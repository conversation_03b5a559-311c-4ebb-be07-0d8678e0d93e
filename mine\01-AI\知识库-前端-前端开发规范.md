
# 前端开发规范

## 一、风格约束

### 设计要求

- **UI和UX风格**：完全遵循苹果官方的人机界面指南（Human Interface Guidelines, HIG），并利用MUI的theme.js中预定义的多种系统能力，整体视觉和交互体验高度贴合Apple生态。
- **页面风格**：统一为light（明亮）主题。

### 设计核心原则

- **清晰性（Clarity）**：界面元素一目了然，信息层级分明。
- **内容优先（Deference）**：UI为内容服务，减少干扰，突出核心信息。
- **空间感（Depth）**：通过层级、动效、阴影等营造空间与过渡。

### 视觉与交互细节

- 极简、精致、注重细节
- 统一的圆角、色彩体系、动效标准
- 布局简洁，交互直观
- 充分考虑无障碍

### 参考标准

- 请参考iOS/macOS原生应用的视觉与交互风格，确保产品在美观性、易用性和一致性上达到苹果官方标准。

---

## 二、技术约束

- 项目技术栈：MUI(v7) + Nextjs(v15)
- 默认采用客户端渲染，需在文件开头添加 `"use client"` 指令
- 所有路径引入需要使用 `@`，根路径为 `src`
- UI 构建请使用 MUI
- 图标库：lucide-react
- 图表库：recharts
- 日期控件：@mui/x-date-pickers
- 时间处理：dayjs
- 占位图片格式：  
  `https://placehold.co/300x200/背景颜色/文字颜色?text=content`  
  其中 text 内容用英文

---

## 三、通知使用

```js
import { useSnackbar } from 'notistack';
const { enqueueSnackbar } = useSnackbar();
enqueueSnackbar("这里是消息", { variant: 'success' });
```

---

## 四、MUI(v7) Grid 使用规范

### 1. 升级原因

- **CSS 变量**：新版 Grid 使用 CSS 变量，减少选择器特异性问题
- **sx 属性**：可通过 `sx` 灵活控制样式
- **无需 item 属性**：所有 Grid 默认视为 item，无需显式指定
- **偏移功能增强**：offset 功能更灵活
- **嵌套深度无限制**：支持任意深度嵌套
- **无负边距**：避免溢出问题

---

### 2. 升级步骤

#### 2.1 更新导入语句

```js
// 旧版
import Grid from '@mui/material/GridLegacy';
// 新版
import Grid from '@mui/material/Grid';
```

#### 2.2 移除旧版属性

`item` 和 `zeroMinWidth` 属性已被移除，直接删除即可：

```js
// 旧版
<Grid item zeroMinWidth>
// 新版
<Grid>
```

#### 2.3 更新尺寸属性

新版统一使用 `size` 对象：

```js
// 旧版
<Grid xs={12} sm={6}>
// 新版
<Grid size={{ xs: 12, sm: 6 }}>
```

所有断点尺寸相同时可简写：

```js
// 旧版
<Grid xs={6}>
// 新版
<Grid size={6}>
```

`true` 值改为 `"grow"`：

```js
// 旧版
<Grid xs>
// 新版
<Grid size="grow">
```

#### 2.4 列方向布局

`direction="column"` 或 `direction="column-reverse"` 在 GridLegacy 和新版 Grid 均不支持。如需垂直布局，请参考官方文档。

#### 2.5 容器宽度

新版 Grid 默认不会自动撑满容器宽度，如需撑满可用 `sx` 属性：

```js
// 旧版
<GridLegacy container>
// 新版
<Grid container sx={{ width: '100%' }}>
// 或父级为 flex 容器时
<Grid container sx={{ flexGrow: 1 }}>
```

---

如需进一步细化或补充内容，请告知！