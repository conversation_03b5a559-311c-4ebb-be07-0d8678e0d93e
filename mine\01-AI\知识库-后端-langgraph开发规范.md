# LangGraph 开发规范

## 概述

LangGraph 是一个用于构建多智能体对话流和有状态应用的框架。本规范基于化妆品图片生成项目的实践经验，提供标准化的开发指导。

## 1. 项目结构规范

### 1.1 标准目录结构

```
agent/
├── __init__.py                    # 包初始化
├── graph.py                      # 工作流图定义
├── state.py                      # 状态管理
├── node/                         # 节点处理模块
│   ├── __init__.py
│   ├── node_name_1.py           # 具体节点实现
│   ├── node_name_2.py
│   └── ...
└── utils/                        # 工具模块
    ├── __init__.py
    ├── helper_class_1.py         # 工具类
    ├── helper_class_2.py
    └── resources/                # 静态资源
```

### 1.2 文件命名规范

- **节点文件**：使用 `功能描述_node.py` 格式，如 `classify_image_node.py`
- **工具文件**：使用 `功能描述_工具类型.py` 格式，如 `image_text_styler.py`
- **状态文件**：统一命名为 `state.py`
- **图定义文件**：统一命名为 `graph.py`

#### 1.2.1 节点文件与函数名一致性规范

**重要原则：节点文件名与其内部主要函数名必须保持一致**

- **文件名格式**：`{功能描述}_node.py`
- **函数名格式**：`{功能描述}`（与文件名保持一致，但去掉`_node.py`后缀）
- **图中节点名**：与函数名一致

**正确示例：**
```
文件名：content_generation_node.py
函数名：async def content_generation(state: OverallState) -> Dict[str, Any]
图中引用：builder.add_node("content_generation", content_generation)
```

**错误示例：**
```
文件名：content_generation_node.py  
函数名：async def generate_comprehensive_content(state: OverallState)  # ❌ 不一致
图中引用：builder.add_node("generate_content", generate_comprehensive_content)  # ❌ 不一致
```

**完整映射表：**
| 文件名 | 函数名 | 图中节点名 |
|--------|--------|------------|
| `image_classification_node.py` | `image_classification` | `"image_classification"` |
| `content_generation_node.py` | `content_generation` | `"content_generation"` |
| `final_integration_node.py` | `final_integration` | `"final_integration"` |
| `text_poster_processing_node.py` | `text_poster_processing` | `"text_poster_processing"` |

这种命名规范的优势：
- **一致性**：从文件名即可推断函数名
- **可维护性**：降低重构和维护成本
- **可读性**：代码结构更加清晰
- **团队协作**：统一的命名约定减少沟通成本

## 2. 状态管理规范

### 2.1 状态定义原则

使用 Pydantic BaseModel 和 Field 定义所有状态结构，分离输入、输出和整体状态。

```python
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class InputState(BaseModel):
    """输入状态定义"""
    material_image_path: str = Field(description="输入的原始图片路径")

class OutputState(BaseModel):
    """输出状态定义"""
    # 来自 playwright_screenshot_node
    final_image_path: Optional[str] = Field(default=None, description="最终输出的图片路径")

class PhotoProcessingState(BaseModel):
    """照片流程专用状态"""
    # 来自 photo_text_recognition_node
    photo_text_content: Optional[str] = Field(default=None, description="照片中识别的文字内容")
    photo_interface_elements: Optional[str] = Field(default=None, description="照片视觉元素分析结果")
    # 来自 photo_content_generation_node
    photo_generated_content: Optional[str] = Field(default=None, description="生成的营销文案内容")
    # 来自 photo_clone_node
    photo_cloned_image_path: Optional[str] = Field(default=None, description="图片复刻后的路径")
    photo_security_blocked_flag: Optional[bool] = Field(default=None, description="安全拦截标记")
    photo_final_image_path: Optional[str] = Field(default=None, description="照片流程最终图片路径")
    # 来自 photo_description_node
    photo_image_description: Optional[str] = Field(default=None, description="图像描述（用于重新生成）")
    # 来自 photo_regeneration_node
    photo_regenerated_image_path: Optional[str] = Field(default=None, description="重新生成的图片路径")
    # 来自 photo_html_generation_node
    photo_html_file_path: Optional[str] = Field(default=None, description="HTML文件路径")
    photo_screenshot_area_id: Optional[str] = Field(default=None, description="截图区域ID")

class PosterProcessingState(BaseModel):
    """海报流程专用状态"""
    # 来自 poster_text_recognition_node
    poster_text_content: Optional[str] = Field(default=None, description="完整版本文字内容")
    poster_interface_elements: Optional[str] = Field(default=None, description="界面元素分析结果")
    # 来自 poster_content_generation_node
    poster_generated_content: Optional[str] = Field(default=None, description="生成的营销文案内容")
    # 来自 poster_html_generation_node
    poster_html_file_path: Optional[str] = Field(default=None, description="HTML文件路径")
    poster_screenshot_area_id: Optional[str] = Field(default=None, description="截图区域ID")

class OverallState(InputState, OutputState, PhotoProcessingState, PosterProcessingState):
    """整体状态定义，包含所有中间处理结果"""
    
    # 来自 image_classification_node
    image_category: Optional[str] = Field(default=None, description="图像分类结果（照片/文字海报/界面截图）")
```

### 2.2 状态变量命名与注释规范

#### 2.2.1 变量命名规范
- **流程前缀**：按处理流程添加前缀（如 `photo_*`, `poster_*`）
- **功能后缀**：按变量用途添加后缀（如 `*_content`, `*_path`, `*_flag`）
- **统一格式**：使用下划线分隔的小写命名

#### 2.2.2 注释规范
**双层注释结构：**
1. **上方注释**：标明变量来自哪个node（`# 来自 node_name`）
2. **Field description**：Pydantic字段的完整描述

**注释格式：**
```python
# 来自 node_name
variable_name: Optional[str] = Field(default=None, description="变量的详细描述")
```

#### 2.2.3 变量顺序规范
- **执行顺序一致**：变量定义顺序与node执行顺序保持一致
- **分组组织**：同一node产生的变量组织在一起
- **逻辑清晰**：按处理流程的先后顺序排列

### 2.3 状态设计最佳实践

- **Field描述**：所有字段必须使用Field并添加description
- **类型明确**：所有字段都要明确类型注解
- **可选字段**：中间状态使用 `Optional` 类型
- **默认值**：为可选字段提供合理的默认值
- **文档字符串**：为每个状态类添加清晰的说明
- **字段验证**：使用 Pydantic 的验证功能确保数据正确性
- **来源标记**：每个变量都要清楚标记来自哪个node

## 3. 节点开发规范

### 3.1 节点开发规范

- **函数命名**：**【核心要求】** 函数名必须与文件名保持一致（去掉`_node.py`后缀）
- **异步函数**：所有节点函数必须是异步函数
- **参数类型**：接收 `OverallState` 参数
- **返回类型**：返回 `Dict[str, Any]` 用于状态更新
- **日志记录**：使用统一的日志格式记录执行过程
- **错误处理**：不捕获异常，让异常向上传播给外层调用方
- **参数验证**：对输入参数进行验证，发现问题直接抛出异常
- **文档注释**：清晰的函数文档说明

#### 3.1.1 函数命名一致性要求

**强制性规范：**
```python
# 文件: content_generation_node.py
async def content_generation(state: OverallState) -> Dict[str, Any]:  # ✅ 正确
    """内容生成节点"""
    pass

# 错误示例：
async def generate_comprehensive_content(state: OverallState) -> Dict[str, Any]:  # ❌ 错误
    """函数名与文件名不一致"""
    pass
```

**验证方法：**
- 文件名：`{function_name}_node.py`
- 函数名：`{function_name}`
- 图中引用：`builder.add_node("{function_name}", {function_name})`

这确保了从文件名就能直接推断出函数名，提高代码的可维护性和一致性。

### 3.2 标准节点模板

```python
import asyncio
from typing import Dict, Any
from omni.log.log import olog
from ..state import OverallState

async def example_processing(state: OverallState) -> Dict[str, Any]:
    """
    示例处理节点（对应文件: example_processing_node.py）
    
    Args:
        state: 整体状态对象
        
    Returns:
        Dict[str, Any]: 状态更新字典
    """
    olog.info(f"开始执行 {example_processing.__name__} 节点")
    
    # 1. 从状态中获取输入数据
    input_data = state.field_name
    
    # 2. 参数验证 - 发现问题直接抛出异常
    if not input_data:
        raise ValueError("输入数据为空")
    
    # 3. 执行具体业务逻辑 - 不捕获异常
    # 在这里直接实现具体的处理逻辑
    result = await some_processing(input_data)
    
    # 4. 返回状态更新
    olog.info(f"{example_processing.__name__} 节点执行完成")
    return {"output_field": result}
```

### 3.3 条件判断节点

```python
def condition_function(state: OverallState) -> str:
    """
    条件判断函数
    
    Args:
        state: 整体状态对象
        
    Returns:
        str: 条件分支名称
    """
    classification = state.image_classification
    
    if classification == "美妆产品":
        return "beauty_product_branch"
    elif classification == "人像":
        return "portrait_branch"
    else:
        return "other_branch"
```

### 3.4 提示词工程规范

提示词设计应采用变量形式，遵循 #角色 #背景 #任务 #约束 的结构：

```python
# 图像分析提示词模板
IMAGE_ANALYSIS_PROMPT = """
# 角色
你是一个专业的图像分析师，擅长分析产品图片和场景图像。

# 背景
需要对电商平台的产品图像进行自动化分析和分类，以支持内容管理和推荐系统。
当前处理的图像描述：{image_description}
相关产品信息：{product_info}

# 任务
对给定的图像进行详细分析和分类，识别关键特征并提供准确的分类结果。
需要从以下类别中选择：美妆产品、人像、场景、其他。

# 约束
- 仔细分析图像中的主要元素和特征
- 结合产品信息进行综合判断
- 提供准确的分类结果和置信度评估
- 详细说明分析推理过程
- 严格按照JSON格式返回结果：{{"category": "分类结果", "confidence": 0.95, "features": ["特征1", "特征2"], "reasoning": "推理过程"}}

"""

# 文本生成提示词模板
TEXT_GENERATION_PROMPT = """
# 角色
你是一个专业的内容创作者，擅长创作各类营销文案和产品描述。

# 背景
需要为电商平台的产品创作吸引人的营销文案，目标受众是{target_audience}。
产品信息：{product_info}

# 任务
基于产品信息创作高质量的营销文案，要求内容吸引人且符合品牌调性。

# 约束
- 文案风格要{style_requirement}
- 字数控制在{word_limit}字以内
- 突出产品核心卖点
- 语言要简洁有力，易于理解
- 直接输出文案内容，不要添加多余说明

"""
```

**关键原则：**
- 使用变量形式定义提示词模板
- 采用#角色#背景#任务#约束的四部分结构
- 通过 `.format()` 或 f-string 动态填充变量内容
- 提示词要简洁明确，避免冗余表述

### 3.5 AI模型调用规范

在LangGraph节点中调用AI模型有三种标准方式，每种方式适用于不同的使用场景：

- **structured_output_handler**：用于需要结构化数据返回的场景，如数据分析、分类等
- **text_output_handler**：用于需要纯文本返回的场景，如文案生成、摘要等  
- **text2structured_output_handler**：用于将非结构化文本转换为结构化数据的场景

所有AI调用都基于Pydantic模型进行输出验证和类型约束。

#### 3.5.1 structured_output_handler - 结构化输出处理

**使用场景：** 需要AI返回具有明确结构的数据，如分类结果、分析报告等。

**函数签名：**
```python
async def structured_output_handler(
    prompt_template: str,           # 必填：提示词模板
    params: Dict[str, Any],         # 必填：模板参数字典
    output_model: Type[T],          # 必填：输出的Pydantic模型类
    llm_name: Optional[str] = None, # 选填：LLM模型名称，默认使用配置中的默认模型
    tags: Optional[List[str]] = None, # 选填：标签列表，默认为["structured_output_agent"]
    image_path: Optional[str] = None, # 选填：图片路径，支持多模态输入
) -> T:
```

**完整使用示例：**
```python
from pydantic import BaseModel, Field
from typing import List
from omni.llm.output_agent import structured_output_handler

# 1. 定义输出模型
class ImageAnalysisResult(BaseModel):
    category: str = Field(description="图像分类结果")
    confidence: float = Field(ge=0.0, le=1.0, description="置信度")
    features: List[str] = Field(default_factory=list, description="识别的特征")
    reasoning: str = Field(description="分析推理过程")

# 2. 定义提示词模板
prompt_template = """
# 角色
你是专业的图像分析师。

# 任务
分析图像：{image_description}
产品信息：{product_info}

请对图像进行分类并提供分析结果。

"""

# 3. 准备参数
params = {
    "image_description": "一张化妆品产品图片",
    "product_info": "口红，品牌为YSL"
}

# 4. 调用示例 - 纯文本模式
result = await structured_output_handler(
    prompt_template=prompt_template,    # 必填
    params=params,                      # 必填
    output_model=ImageAnalysisResult,   # 必填
    llm_name="gpt-4o-mini",            # 选填
    tags=["image_analysis", "cosmetic"], # 选填
)

# 5. 调用示例 - 多模态模式（包含图片）
result_with_image = await structured_output_handler(
    prompt_template=prompt_template,    # 必填
    params=params,                      # 必填
    output_model=ImageAnalysisResult,   # 必填
    llm_name="gpt-4o",                 # 选填（多模态建议用gpt-4o）
    tags=["image_analysis"],           # 选填
    image_path="/path/to/image.jpg",   # 选填（多模态输入）
)

# 结果使用
print(f"分类：{result.category}")
print(f"置信度：{result.confidence}")
print(f"特征：{result.features}")
```

**关键要点：**
- 使用 `Field` 添加字段描述和验证约束
- 通过 `@validator` 装饰器添加自定义验证逻辑
- 为数值字段设置合理的范围约束（如 `ge=0.0, le=1.0`）
- 使用 `default_factory` 为列表字段提供默认值
- `{format_instructions}` 会自动添加到模板末尾，无需手动添加

#### 3.5.2 text_output_handler - 文本输出处理

**使用场景：** 需要AI返回纯文本内容的场景，如文案生成、摘要、翻译等。

**函数签名：**
```python
async def text_output_handler(
    prompt_template: str,           # 必填：提示词模板
    params: Dict[str, Any],         # 必填：模板参数字典
    llm_name: Optional[str] = None, # 选填：LLM模型名称，默认使用配置中的默认模型
    tags: Optional[List[str]] = None, # 选填：标签列表，默认为["text_output_handler"]
    image_path: Optional[str] = None, # 选填：图片路径，支持多模态输入
) -> str:
```

**完整使用示例：**
```python
from omni.llm.output_agent import text_output_handler

# 1. 定义提示词模板
prompt_template = """# 角色
你是专业的文案创作者。

# 任务
为以下产品创作营销文案：
产品名称：{product_name}
产品特点：{product_features}
目标用户：{target_audience}

# 要求
- 文案风格要{style}
- 字数控制在{word_limit}字以内
- 突出产品核心卖点
- 直接输出文案内容，不要添加多余说明"""

# 2. 准备参数
params = {
    "product_name": "YSL口红",
    "product_features": "持久不脱色，显色度高",
    "target_audience": "25-35岁职场女性",
    "style": "优雅时尚",
    "word_limit": "100"
}

# 3. 调用示例 - 纯文本模式
text_result = await text_output_handler(
    prompt_template=prompt_template,  # 必填
    params=params,                    # 必填
    llm_name="gpt-3.5-turbo",        # 选填
    tags=["copywriting", "marketing"], # 选填
)

# 4. 调用示例 - 多模态模式（基于图片生成文案）
text_with_image = await text_output_handler(
    prompt_template=prompt_template,  # 必填
    params=params,                    # 必填
    llm_name="gpt-4o",               # 选填（多模态建议用gpt-4o）
    tags=["copywriting"],            # 选填
    image_path="/path/to/product.jpg", # 选填
)

# 结果使用
print(f"生成的文案：{text_result}")
```

**关键要点：**
- 返回类型为纯字符串，适合文本生成类任务
- 支持多模态输入，可结合图片生成文案
- 不需要定义输出模型，直接返回文本内容
- 适合创意写作、摘要生成、翻译等场景

#### 3.5.3 text2structured_output_handler - 文本转结构化处理

**使用场景：** 将非结构化的原始文本数据转换为结构化的Pydantic模型，如解析爬虫数据、整理用户输入等。

**函数签名：**
```python
async def text2structured_output_handler(
    output_model: Type[T],  # 必填：输出的Pydantic模型类
    raw_data: str,          # 必填：原始文本数据
) -> T:
```

**完整使用示例：**
```python
from pydantic import BaseModel, Field
from typing import List, Optional
from omni.llm.output_agent import text2structured_output_handler

# 1. 定义输出模型
class ProductInfo(BaseModel):
    name: str = Field(description="产品名称")
    brand: str = Field(description="品牌")
    price: Optional[float] = Field(description="价格", default=None)
    features: List[str] = Field(default_factory=list, description="产品特点")
    category: str = Field(description="产品类别")

# 2. 准备原始数据（比如从爬虫或其他渠道获得的非结构化文本）
raw_text = """
这款YSL圣罗兰口红，型号416，价格298元。
主要特点包括：持久不脱色、显色度高、质地丝滑。
属于彩妆类别，专为现代女性设计。
包装精美，是送礼佳品。
"""

# 3. 调用示例
structured_result = await text2structured_output_handler(
    output_model=ProductInfo,  # 必填
    raw_data=raw_text,         # 必填
)

# 结果使用
print(f"产品名称：{structured_result.name}")
print(f"品牌：{structured_result.brand}")
print(f"价格：{structured_result.price}")
print(f"特点：{structured_result.features}")
print(f"类别：{structured_result.category}")
```

**关键要点：**
- 专门用于数据清洗和结构化处理
- 不需要自定义提示词，内置标准化提示模板
- 适合处理爬虫数据、用户输入、文档解析等场景
- 输出严格遵循定义的Pydantic模型结构


### 3.6 节点职责单一化规范

**强制性规范：每个节点只能调用一次 `omni.llm.output_agent` 方法**

在LangGraph应用开发中，严格遵循"一个节点一次AI调用"的原则：

- **单一AI调用**：每个节点函数只能包含一次对 `omni.llm.output_agent` 的调用
- **强制拆分**：包含多次AI调用的节点必须拆分为多个独立节点
- **职责分离**：每个节点专注于一个特定的AI处理任务

**正确示例：**
```python
# ✅ 符合规范 - 每个节点只调用一次AI
async def extract_text(state: OverallState) -> Dict[str, Any]:
    result = await structured_output_handler(...)  # 只有一次AI调用
    return {"extracted_text": result}

async def generate_content(state: OverallState) -> Dict[str, Any]:
    content = await text_output_handler(...)  # 只有一次AI调用
    return {"generated_content": content}
```

**错误示例：**
```python
# ❌ 违反规范 - 同一节点包含多次AI调用
async def process_content(state: OverallState) -> Dict[str, Any]:
    analysis = await structured_output_handler(...)  # 第1次AI调用
    content = await text_output_handler(...)         # 第2次AI调用  ❌ 违规
    return {"analysis": analysis, "content": content}
```


### 3.7 完整AI节点开发示例

这是一个包含模型调用、提示词工程、结构化输出的完整节点示例：

```python
import asyncio
from typing import Dict, Any, Optional, List
from omni.log.log import olog
from omni.llm.output_agent import structured_output_handler
from pydantic import BaseModel, Field, validator
from ..state import OverallState

# 提示词模板（采用变量形式）
IMAGE_ANALYSIS_PROMPT = """# 角色
你是一个专业的图像分析师，擅长分析产品图片和场景图像。

# 背景
需要对电商平台的产品图像进行自动化分析和分类，以支持内容管理和推荐系统。
当前处理的图像描述：{image_description}
相关产品信息：{product_info}

# 任务
对给定的图像进行详细分析和分类，识别关键特征并提供准确的分类结果。
需要从以下类别中选择：美妆产品、人像、场景、其他。

# 约束
- 仔细分析图像中的主要元素和特征
- 结合产品信息进行综合判断
- 提供准确的分类结果和置信度评估
- 详细说明分析推理过程
- 严格按照JSON格式返回结果：{{"category": "分类结果", "confidence": 0.95, "features": ["特征1", "特征2"], "reasoning": "推理过程"}}
"""

class ImageAnalysisResponse(BaseModel):
    """图像分析响应结构"""
    category: str = Field(description="图像分类结果")
    confidence: float = Field(ge=0.0, le=1.0, description="置信度分数")
    features: List[str] = Field(default_factory=list, description="识别的特征")
    reasoning: str = Field(description="分析推理过程")

async def image_analysis_node(state: OverallState) -> Dict[str, Any]:
    """
    图像分析节点 - 完整的AI调用、提示词工程、结构化输出示例
    
    Args:
        state: 整体状态对象
        
    Returns:
        Dict[str, Any]: 状态更新字典
    """
    olog.info("开始执行图像分析节点")
    
    # 1. 从状态中获取输入数据
    image_description = state.image_description
    product_info = state.product_info
    
    # 2. 参数验证
    if not image_description:
        raise ValueError("图像描述为空")
    if not product_info:
        raise ValueError("产品信息为空")
    
    # 3. 使用变量形式的提示词
    prompt = IMAGE_ANALYSIS_PROMPT.format(
        image_description=image_description,
        product_info=product_info
    )
    
    # 4. 调用AI模型获取结构化输出
    analysis_response = await structured_output_handler(
        prompt=prompt,
        response_model=ImageAnalysisResponse,
        model_name="gpt-4o-mini"  # 根据任务复杂度选择
    )
    
    # 5. 验证AI响应
    if not analysis_response or not analysis_response.category:
        raise RuntimeError("AI模型返回了无效的分析结果")
    
    # 6. 返回状态更新
    olog.info(f"图像分析完成，分类: {analysis_response.category}")
    return {
        "image_category": analysis_response.category,
        "analysis_confidence": analysis_response.confidence,
        "image_features": analysis_response.features,
        "analysis_reasoning": analysis_response.reasoning
    }
```

## 4. 图构建规范

### 4.1 标准图构建模板

```python
from langgraph.graph import StateGraph, END
from .state import OverallState, InputState, OutputState
from .node.image_classification_node import image_classification
from .node.content_generation_node import content_generation
from .node.final_integration_node import final_integration
from .node.text_poster_processing_node import text_poster_processing
from .node.product_overlay_processing_node import product_overlay_processing

def create_workflow_graph():
    """创建工作流图"""
    # 创建图构建器
    builder = StateGraph(
        OverallState, 
        input_schema=InputState, 
        output_schema=OutputState
    )
    
    # 添加节点 - 节点名称与函数名保持一致
    builder.add_node("image_classification", image_classification)
    builder.add_node("text_poster_processing", text_poster_processing)
    builder.add_node("product_overlay_processing", product_overlay_processing)
    builder.add_node("content_generation", content_generation)
    builder.add_node("final_integration", final_integration)
    
    # 设置入口点
    builder.set_entry_point("image_classification")
    
    # 添加条件边
    builder.add_conditional_edges(
        "image_classification",
        create_presentation_condition,
        {
            "text_poster_branch": "text_poster_processing",
            "product_overlay_branch": "product_overlay_processing"
        }
    )
    
    # 添加直接边
    builder.add_edge("text_poster_processing", "content_generation")
    builder.add_edge("product_overlay_processing", "content_generation")
    builder.add_edge("content_generation", "final_integration")
    builder.add_edge("final_integration", END)
    
    return builder.compile()

def create_presentation_condition(state: OverallState) -> str:
    """
    根据图片呈现方式判断条件分支
    
    Args:
        state: 整体状态对象
        
    Returns:
        str: 条件分支名称
    """
    presentation_type = state.image_presentation_type
    
    if presentation_type == "文字海报":
        return "text_poster_branch"
    elif presentation_type == "产品图上放字":
        return "product_overlay_branch"
    else:
        # 默认分支
        return "text_poster_branch"
```

### 4.2 图构建最佳实践

- **清晰命名**：节点名称要清晰表达功能
- **入口点**：明确设置工作流入口点
- **条件分支**：合理设计条件分支逻辑
- **边的连接**：确保所有路径都能正确结束
- **错误处理**：考虑异常情况的处理路径

## 5. 工具函数开发规范

### 5.1 工具函数标准结构

```python
import asyncio
from typing import Optional, Dict, Any
from omni.log.log import olog

async def async_utility_function(
    input_data: Any, 
    config: Dict[str, Any],
    **kwargs
) -> Optional[Any]:
    """
    异步工具函数
    
    Args:
        input_data: 输入数据
        config: 配置参数
        **kwargs: 其他可选参数
        
    Returns:
        Optional[Any]: 处理结果，失败时返回None
    """
    olog.info(f"开始执行 {async_utility_function.__name__}")
    
    # 参数验证
    if not input_data:
        raise ValueError("输入数据为空")
    
    # 执行具体处理逻辑
    result = await _process_data(input_data, config)
    
    olog.info(f"{async_utility_function.__name__} 执行完成")
    return result

def sync_utility_function(
    input_data: Any, 
    config: Dict[str, Any],
    **kwargs
) -> Optional[Any]:
    """
    同步工具函数
    
    Args:
        input_data: 输入数据
        config: 配置参数
        **kwargs: 其他可选参数
        
    Returns:
        Optional[Any]: 处理结果，失败时返回None
    """
    olog.info(f"开始执行 {sync_utility_function.__name__}")
    
    # 参数验证
    if not input_data:
        raise ValueError("输入数据为空")
    
    # 执行具体处理逻辑
    result = _sync_process_data(input_data, config)
    
    olog.info(f"{sync_utility_function.__name__} 执行完成")
    return result

# 私有辅助函数
async def _process_data(data: Any, config: Dict[str, Any]) -> Any:
    """私有异步处理函数"""
    # 具体实现逻辑
    pass

def _sync_process_data(data: Any, config: Dict[str, Any]) -> Any:
    """私有同步处理函数"""
    # 具体实现逻辑
    pass
```

### 5.2 工具函数设计原则

- **单一职责**：每个工具函数专注一个特定功能
- **参数明确**：通过函数参数传递配置和数据
- **异步优先**：优先提供异步函数实现
- **错误传播**：不捕获异常，让异常向上传播
- **命名清晰**：函数名称要清晰表达功能意图

## 6. 错误处理规范

### 6.1 统一错误处理原则

**核心原则：node函数内不处理错误，所有异常统一由外层调用方处理**

```python
# 正确的node实现 - 不捕获异常，直接抛出
async def node_function(state: OverallState) -> Dict[str, Any]:
    """
    节点函数不处理异常，让异常向上传播
    """
    olog.info(f"开始执行 {node_function.__name__} 节点")
    
    # 1. 从状态中获取输入数据
    input_data = state.field_name
    
    # 2. 参数验证 - 发现问题直接抛出异常
    if not input_data:
        raise ValueError("输入数据为空")
    
    # 3. 执行具体业务逻辑 - 不捕获异常
    result = await process_logic(input_data)
    
    # 4. 返回状态更新
    olog.info(f"{node_function.__name__} 节点执行完成")
    return {"output_field": result}

# 工具函数也遵循相同原则
async def utility_function(input_data: Any, config: Dict[str, Any]) -> Any:
    """工具函数不捕获异常，直接抛出"""
    # 具体处理逻辑，异常直接向上传播
    result = await _process_data(input_data, config)
    return result

# 统一由外层调用方处理异常
async def execute_workflow(graph, input_data):
    """外层调用方统一处理所有异常"""
    try:
        result = await graph.ainvoke(input_data)
        return {"success": True, "data": result}
    except ValueError as e:
        olog.error(f"参数错误: {str(e)}")
        return {"success": False, "error": "invalid_input", "message": str(e)}
    except APIException as e:
        olog.error(f"API调用失败: {str(e)}")
        return {"success": False, "error": "api_error", "message": str(e)}
    except Exception as e:
        olog.error(f"未知错误: {str(e)}")
        return {"success": False, "error": "unknown_error", "message": str(e)}
```

### 6.2 错误处理最佳实践

- **统一处理**：所有异常由最外层调用方统一捕获和处理
- **快速失败**：node发现问题立即抛出异常，不尝试修复
- **日志记录**：在外层处理时详细记录错误信息
- **异常分类**：根据异常类型返回不同的错误码和信息
- **清晰传播**：让异常清晰地从底层传播到顶层

## 7. 性能优化规范

### 7.1 异步处理要求

**重要：所有LangGraph应用必须使用异步处理**

- **节点函数**：所有节点函数必须定义为异步函数 `async def`
- **工具类方法**：工具类的主要方法应该支持异步调用
- **外部调用**：所有外部API调用、数据库操作、文件IO等必须使用异步方式
- **图执行**：使用 `graph.ainvoke()` 而不是 `graph.invoke()`

```python
# 正确的异步实现
async def node_function(state: OverallState) -> Dict[str, Any]:
    """所有节点函数必须是异步的"""
    result = await some_async_operation()
    return {"output": result}

# 工具类也要支持异步
class UtilityClass:
    async def process(self, data):
        """主要处理方法使用异步"""
        return await self._async_operation(data)
```

其他性能优化策略根据具体业务场景和性能要求进行实施。