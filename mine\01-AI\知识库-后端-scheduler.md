# scheduler 编写示例

```python
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.msg_queue.redis_set_publisher import publish_messages_to_redis_set
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler

#实际运行的方法
async def execute_task() -> None:
    pass

# 使用 @register_scheduler 装饰器注册调度任务
# trigger='interval' 表示以固定时间间隔执行
# seconds=5*60 表示每5分钟执行一次
@register_scheduler(trigger='interval', seconds=5 * 60)
class UserScheduler(BaseScheduler):  # 必须继承 BaseScheduler
    # run_task 方法为调度器实际执行的任务内容
    async def run_task(self):
        await execute_task()

# 方便测试
if __name__ == "__main__":
    import asyncio
    
    async def main():
        await init_models()
        await execute_task()
    
    asyncio.run(main())
```
