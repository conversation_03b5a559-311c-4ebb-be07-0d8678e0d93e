# 数据库定义规范

## 概述

本文档基于 `common-module/models/models.py` 文件总结项目的数据库定义规范和最佳实践。

## 1. 基础设计原则

### 1.1 模型继承规范
- **Document类**: 所有数据库实体模型必须继承自 `beanie.Document`
- **BaseModel类**: 用于嵌套对象和数据传输对象，继承自 `pydantic.BaseModel`

```python
from beanie import Document
from pydantic import BaseModel, Field

# 数据库实体
class User(Document):
    username: Optional[str] = Field(default=None, title="用户名")
    
# 嵌套对象
class ImageInfo(BaseModel):
    oss_key: Optional[str] = Field(default=None, title="OSS存储键")
    order: Optional[int] = Field(default=None, title="排序")
```

### 1.2 字段定义规范
- **可选字段**: 使用 `Optional[Type]` 标注，默认值设为 `None`
- **字段描述**: 必须使用 `Field(title="中文描述")` 为每个字段添加中文说明
- **默认值**: 明确指定 `default=None` 或其他合适的默认值

## 2. 数据类型规范

### 2.1 支持的平台类型
```python
SupportedPlatform = Literal["小红书", "抖音", "公众号", "今日头条", "知乎"]
```

### 2.2 状态枚举规范
使用 `Literal` 类型定义状态枚举，统一使用中文描述：

```python
# 账号状态
status: Optional[Literal["在线", "离线", "封禁"]] = Field(default=None, title="账号状态")

# 任务状态
fetch_status: Optional[Literal["待爬取", "爬取中", "已完成", "失败"]] = Field(default=None, title="素材获取状态")

# 验证状态
validation_status: Optional[Literal["待验证", "成功", "失败"]] = Field(default=None, title="验证状态")
```

### 2.3 时间字段规范
- 统一使用 **时间戳** (int) 存储时间
- 命名约定：
  - `create_at`: 创建时间
  - `last_updated_at`: 最后更新时间
  - `published_at`: 发布时间
  - `settled_at`: 结算时间
  - `analyzed_at`: 分析时间

```python
create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")
last_updated_at: Optional[int] = Field(default=None, title="最后更新时间")
```

## 3. 关联关系规范

### 3.1 外键命名约定
- 格式：`{关联表名}_id`
- 类型：`Optional[str]`
- 示例：
  - `user_id`: 关联User表
  - `product_id`: 关联Product表
  - `account_id`: 关联Account表
  - `promotion_task_detail_id`: 关联PromotionTaskDetail表

```python
user_id: Optional[str] = Field(default=None, title="关联的 User 的ID(广告主)")
product_id: Optional[str] = Field(default=None, title="关联的 Product 的ID")
```

### 3.2 关联说明规范
在title中明确说明关联对象的角色：
- `title="关联的 User 的ID(广告主)"`
- `title="关联的 User 的ID(流量主)"`
- `title="关联的 User 的ID(舰长)"`

## 4. 通用字段规范

### 4.1 必备通用字段
每个Document模型建议包含以下通用字段：

```python
create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")
is_deleted: Optional[bool] = Field(default=None, title="删除标记")
```

### 4.2 用户角色字段
```python
roles: Optional[List[Literal["user", "creator", "captain", "advertiser", "admin"]]] = Field(
    default=None, title="用户角色列表"
)
```

## 5. 集合命名规范

### 5.1 Settings配置
每个Document类必须包含Settings内部类定义集合名称：

```python
class User(Document):
    # 字段定义...
    
    class Settings:
        name = "user"  # 集合名称使用小写下划线格式
```

### 5.2 命名约定
- 单数形式：`user`, `product`, `account`
- 下划线分隔：`user_basic_material`, `ai_generation_task`
- 简洁明确：避免过长的名称

## 6. 数据转换规范

### 6.1 to_dict方法
每个Document类必须实现统一的to_dict方法：

```python
def to_dict(self):
    data = self.model_dump()
    data["_id"] = str(self.id)
    data.pop("id", None)
    return data
```

## 7. 嵌套对象规范

### 7.1 图片信息
```python
class ImageInfo(BaseModel):
    oss_key: Optional[str] = Field(default=None, title="OSS存储键")
    order: Optional[int] = Field(default=None, title="排序")
    signed_url: Optional[str] = Field(default=None, title="图片签名URL")
```

### 7.2 用户信息
```python
class UgcUserInfo(BaseModel):
    ugc_user_id: Optional[str] = Field(default=None, title="用户ID")
    nickname: Optional[str] = Field(default=None, title="用户昵称")
    image: Optional[str] = Field(default=None, title="用户头像")
```

## 8. 数据类型最佳实践

### 8.1 数值字段
- **金额**: 使用 `float` 类型，添加 `gt=0.0` 验证
- **计数**: 使用 `int` 类型，默认值为 `0`
- **评分**: 明确范围，如 `-10到30分`

```python
amount: Optional[float] = Field(default=None, gt=0.0, title="充值金额")
view_count: Optional[int] = Field(default=None, title="浏览量")
user_value_score: Optional[int] = Field(default=None, title="用户价值评分")
```

### 8.2 文本字段
- **长文本**: 使用 `Optional[str]`
- **枚举文本**: 使用 `Literal` 类型
- **列表字段**: 使用 `Optional[List[Type]]`

### 8.3 布尔字段
```python
is_deleted: Optional[bool] = Field(default=None, title="删除标记")
is_actively_deleted: Optional[bool] = Field(default=None, title="评论是否被主动删除")
```

## 9. 字段描述规范

### 9.1 描述格式
- 使用简洁的中文描述
- 包含数据类型说明(如"时间戳"、"列表")
- 明确字段用途和约束条件

### 9.2 示例
```python
# 好的描述
title="关联的 User 的ID(广告主)"
title="创建时间(时间戳)"
title="用户角色列表"

# 避免的描述
title="ID"
title="时间"
title="状态"
```

## 10. 导入规范

```python
from typing import Any, Dict, List, Literal, Optional
from beanie import Document
from pydantic import Field, BaseModel
```

## 11. 注意事项

1. **一致性**: 保持字段命名、类型定义的一致性
2. **可扩展性**: 预留扩展字段，使用Optional类型
3. **可读性**: 添加详细的中文注释和文档
4. **关联性**: 明确表与表之间的关联关系
5. **验证性**: 适当添加字段验证规则

---

*本规范基于项目实际代码总结，后续开发应严格遵循此规范*