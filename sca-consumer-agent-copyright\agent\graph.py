from langgraph.graph import StateGraph, END
from langgraph.graph.state import CompiledStateGraph

from agent.node.initialize_sandbox_node import initialize_sandbox
from agent.node.create_requirement_outline_node import create_requirement_outline
from agent.node.generate_page_code_node import generate_page_code
from agent.node.deploy_and_check_node import deploy_and_check
from agent.node.ai_fix_pages_node import ai_fix_pages
from agent.node.generate_apply_doc_node import generate_apply_doc
from agent.node.generate_code_doc_node import generate_code_doc
from agent.node.generate_usage_doc_node import generate_usage_doc
from agent.node.generate_design_doc_node import generate_design_doc
from agent.node.compress_and_upload_node import compress_and_upload
from agent.state import OverallState, InputState, OutputState
from omni.log.log import olog


def should_continue_page_check(state: OverallState) -> str:
    """
    判断是否继续页面检查循环（从deploy_and_check节点调用）
    
    Args:
        state: 整体状态
        
    Returns:
        str: 下一个节点名称 ("ai_fix_pages" 或 "generate_apply_doc")
    """
    # 如果有页面错误且未达到最大尝试次数，进行修复
    if (state.has_page_errors and 
        state.page_check_attempts < state.max_page_check_attempts):
        return "ai_fix_pages"
    
    # 如果达到最大尝试次数但仍有错误，记录警告但继续流程
    if state.page_check_attempts >= state.max_page_check_attempts and state.has_page_errors:
        error_msg = f"已达到最大页面检查尝试次数 ({state.max_page_check_attempts})，页面仍有错误，但继续流程"
        olog.warning(error_msg)
    
    # 没有错误或已达到最大尝试次数，进入后续文档生成阶段
    return "generate_apply_doc"




def create_workflow_graph() -> CompiledStateGraph:
    """创建工作流图"""
    # 创建图构建器
    builder = StateGraph(
        OverallState, 
        input_schema=InputState, 
        output_schema=OutputState
    )
    
    # 添加节点
    builder.add_node("initialize_sandbox", initialize_sandbox)
    builder.add_node("create_requirement_outline", create_requirement_outline)
    builder.add_node("generate_page_code", generate_page_code)
    builder.add_node("deploy_and_check", deploy_and_check)
    builder.add_node("ai_fix_pages", ai_fix_pages)
    builder.add_node("generate_apply_doc", generate_apply_doc)
    builder.add_node("generate_code_doc", generate_code_doc)
    builder.add_node("generate_usage_doc", generate_usage_doc)
    builder.add_node("generate_design_doc", generate_design_doc)
    builder.add_node("compress_and_upload", compress_and_upload)
    
    # 设置入口点
    builder.set_entry_point("initialize_sandbox")
    
    # 设置执行路径
    builder.add_edge("initialize_sandbox", "create_requirement_outline")
    builder.add_edge("create_requirement_outline", "generate_page_code")
    builder.add_edge("generate_page_code", "deploy_and_check")
    
    # 部署和检查的条件分支
    builder.add_conditional_edges(
        "deploy_and_check",
        should_continue_page_check,
        {
            "ai_fix_pages": "ai_fix_pages",
            "generate_apply_doc": "generate_apply_doc"
        }
    )
    
    # AI修复后回到部署和检查
    builder.add_edge("ai_fix_pages", "deploy_and_check")
    builder.add_edge("generate_apply_doc", "generate_design_doc")
    builder.add_edge("generate_design_doc", "generate_usage_doc")
    builder.add_edge("generate_usage_doc", "generate_code_doc")
    builder.add_edge("generate_code_doc", "compress_and_upload")
    builder.add_edge("compress_and_upload", END)
    
    # 编译工作流
    return builder.compile()


# 创建全局工作流图实例
graph = create_workflow_graph()