import asyncio
import json
from pathlib import Path
from typing import Dict, Any, List

import aiofiles
from pydantic import BaseModel, Field

from agent.state import OverallState
from agent.utils.path_utils import smart_join_path
from omni.llm.output_agent import structured_output_handler
from omni.log.log import olog


# 修复页面代码的提示模板
FIX_CODE_PROMPT = """
# 角色
你是一名专业的前端开发工程师

# 背景
根据异常信息修复页面代码。
项目：{requirement_outline_json}
路径：{page_path}
页面：{page_name} - {page_description}
说明：{page_usage_instructions}

当前代码：
```typescript
{current_code}
```

异常信息：
{error_info}

# 前端开发规范

## 一、风格约束

### 设计要求

- **UI和UX风格**：完全遵循Google Material Design设计语言，采用现代化的科技感设计风格，整体视觉和交互体验符合Google生态标准。

### 设计核心原则

- **质感设计（Material Design）**：运用纸质隐喻，通过阴影、层次、动效营造真实感。
- **无障碍友好**：遵循WCAG标准，确保可访问性。

### 视觉与交互细节

- 现代化、简洁、注重功能性
- 统一的圆角设计（0.75rem基础圆角）
- Google科技配色体系（已在globals.css中定义）
- 流畅的过渡动效和交互反馈

### 参考标准

- 请参考Google Material Design官方指南，确保产品在美观性、功能性和一致性上达到Google官方标准。

---

## 二、技术约束

### 核心技术栈
- **项目基础**：shadcn/ui + Tailwind CSS + Next.js 15 + TypeScript
- **客户端渲染**：默认采用客户端渲染，需在文件开头添加 `"use client"` 指令

### UI组件库
- **主要组件库**：shadcn/ui,其导入方式@/components/ui/xxx;
- **图标库**：lucide-react（提供丰富的图标选择）
- **图表库**：recharts（用于数据可视化和图表展示）

### 路径引入规范
- 所有路径引入需要使用相对路径或绝对路径
- 组件导入优先使用 `@/` 别名（@/components/ui/xxx';）

## 三、使用到的库

```
"@dnd-kit/core": "^6.3.1",
"@dnd-kit/modifiers": "^9.0.0",
"@dnd-kit/sortable": "^10.0.0",
"@dnd-kit/utilities": "^3.2.2",
"@hookform/resolvers": "^5.2.0",
"@radix-ui/react-accordion": "^1.2.11",
"@radix-ui/react-alert-dialog": "^1.1.14",
"@radix-ui/react-aspect-ratio": "^1.1.7",
"@radix-ui/react-avatar": "^1.1.10",
"@radix-ui/react-checkbox": "^1.3.2",
"@radix-ui/react-collapsible": "^1.1.11",
"@radix-ui/react-context-menu": "^2.2.15",
"@radix-ui/react-dialog": "^1.1.14",
"@radix-ui/react-dropdown-menu": "^2.1.15",
"@radix-ui/react-hover-card": "^1.1.14",
"@radix-ui/react-label": "^2.1.7",
"@radix-ui/react-menubar": "^1.1.15",
"@radix-ui/react-navigation-menu": "^1.2.13",
"@radix-ui/react-popover": "^1.1.14",
"@radix-ui/react-progress": "^1.1.7",
"@radix-ui/react-radio-group": "^1.3.7",
"@radix-ui/react-scroll-area": "^1.2.9",
"@radix-ui/react-select": "^2.2.5",
"@radix-ui/react-separator": "^1.1.7",
"@radix-ui/react-slider": "^1.3.5",
"@radix-ui/react-slot": "^1.2.3",
"@radix-ui/react-switch": "^1.2.5",
"@radix-ui/react-tabs": "^1.1.12",
"@radix-ui/react-toggle": "^1.1.9",
"@radix-ui/react-toggle-group": "^1.1.10",
"@radix-ui/react-tooltip": "^1.2.7",
"@tabler/icons-react": "^3.34.1",
"@tanstack/react-table": "^8.21.3",
"class-variance-authority": "^0.7.1",
"clsx": "^2.1.1",
"cmdk": "^1.1.1",
"date-fns": "^4.1.0",
"embla-carousel-react": "^8.6.0",
"input-otp": "^1.4.2",
"lucide-react": "^0.525.0",
"next": "15.4.2",
"next-themes": "^0.4.6",
"react": "19.1.0",
"react-day-picker": "^9.8.1",
"react-dom": "19.1.0",
"react-hook-form": "^7.61.1",
"react-resizable-panels": "^3.0.3",
"recharts": "^2.15.4",
"sonner": "^2.0.6",
"tailwind-merge": "^3.3.1",
"vaul": "^1.1.2",
"zod": "^4.0.11"
```

# 任务
根据提供的异常信息，参考项目整体需求、当前页面需求与前端开发规范，对当前PC端页面代码进行**BUG修复**，确保页面能够正常运行且符合各项规范。

# 约束
不需要做数据验证
变量名必须使用英文，不能包含中文字符
代码必须严格遵循TypeScript语法规范，所有类型定义正确，确保能够通过TypeScript编译
只能使用lucide-react库中实际存在的图标组件，使用前需验证图标名称的正确性
文本内容中避免使用">"与"<"，防止与JSX语法冲突
asChild属性仅可用于shadcn/ui组件，不可用于原生HTML元素，使用时需正确解构props
Badge组件的variant属性值必须为以下四种之一："default" | "secondary" | "destructive" | "outline"
所有使用 await 的函数必须标记为 async

"""


class PageCodeResult(BaseModel):
    code_content: str = Field(description="页面代码内容")


async def _fix_page_code(page_path: str, errors: List[str], current_code: str, 
                        page_info: Dict[str, Any], requirements_json: str, target_path: str) -> bool:
    """使用AI修复页面代码"""
    olog.info(f"开始修复页面代码: {page_path}")

    params = {
        "requirement_outline_json": requirements_json,
        "page_path": page_path,
        "current_code": current_code,
        "error_info": "\n".join(f"{i+1}. {error}" for i, error in enumerate(errors)),
        "page_name": page_info.get('page_name', ''),
        "page_description": page_info.get('page_description', ''),
        "page_usage_instructions": page_info.get('page_usage_instructions', ''),
    }

    result = await structured_output_handler(
        prompt_template=FIX_CODE_PROMPT,
        params=params,
        output_model=PageCodeResult,
        llm_name="CODER_LLM",
    )

    async with aiofiles.open(target_path, "w", encoding="utf-8") as f:
        await f.write('"use client";\n' + result.code_content)
    
    olog.info(f"页面代码修复成功: {target_path}")
    return True


async def ai_fix_pages(state: OverallState) -> Dict[str, Any]:
    """AI修复页面"""
    olog.info("开始执行AI页面修复")
    
    page_errors = state.page_error_mapping
    requirements_json = json.dumps(state.requirement_outline, ensure_ascii=False, indent=2)
    
    olog.info(f"需要修复 {len(page_errors)} 个异常页面")
    
    tasks = []
    for page_path, errors in page_errors.items():
        target_path = smart_join_path(state.project_dir, page_path, "page.tsx")
        
        # 直接读取页面代码
        async with aiofiles.open(target_path, "r", encoding="utf-8") as f:
            current_code = await f.read()
        
        # 查找页面信息
        page_info = {}
        for module in state.requirement_outline.get('modules', []):
            for page in module.get('pages', []):
                if page.get('page_path') == page_path:
                    page_info = page
                    break
            if page_info:
                break
        
        tasks.append(_fix_page_code(
            page_path, errors, current_code, page_info, requirements_json, target_path
        ))
    
    results = await asyncio.gather(*tasks)
    success_count = sum(results)
    
    olog.info(f"页面修复完成: {success_count}/{len(tasks)}")
    return {}