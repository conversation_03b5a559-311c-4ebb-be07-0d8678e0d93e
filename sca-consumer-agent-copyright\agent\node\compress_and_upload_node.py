import random
import string
import aiofiles
import zipfile
import asyncio
from pathlib import Path
from typing import Dict, Any
from aiopath import AsyncPath

from agent.state import OverallState
from agent.utils.path_utils import smart_join_path
from common_config.common_config import SCA_DOCS_OSS_KEY
from omni.integration.oss.tencent_oss import OSSClient
from omni.log.log import olog

"""
压缩文件并上传到OSS智能体节点
"""


def _create_zip_sync(files_list, docs_dir, zip_file_path):
    """
    同步压缩函数，使用标准库zipfile
    
    Args:
        files_list: 要压缩的文件列表
        docs_dir: 文档根目录
        zip_file_path: 压缩文件输出路径
    """
    with zipfile.ZipFile(zip_file_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
        for file_path in files_list:
            # 计算相对路径
            arcname = Path(file_path).relative_to(docs_dir)
            zipf.write(file_path, arcname=str(arcname))
            olog.debug(f"添加文件到压缩包: {arcname}")


async def compress_and_upload(state: OverallState) -> Dict[str, Any]:
    """
    压缩和上传文件节点函数（对应文件: compress_and_upload_node.py）
    
    Args:
        state: 整体状态对象
        
    Returns:
        Dict[str, Any]: 状态更新字典
    """
    olog.info(f"开始执行 {compress_and_upload.__name__} 节点")

    docs_dir = state.docs_dir
    docs_path = AsyncPath(docs_dir)
    olog.debug(f"待压缩的文档目录: {docs_dir}")

    zip_filename = f"{state.requirement_outline['project_name']}.zip"

    temp_dir = AsyncPath("tmp")
    await temp_dir.mkdir(parents=True, exist_ok=True)
    zip_file_path = temp_dir / zip_filename
    olog.debug(f"压缩文件路径: {zip_file_path}")

    olog.info("开始压缩文件")
    
    # 异步收集所有文件路径
    async def collect_files(path):
        files = []
        async for file_path in path.rglob("*"):
            if await file_path.is_file():
                files.append(str(file_path))  # 转换为字符串路径
        return files
    
    all_files = await collect_files(docs_path)
    
    try:
        # 使用同步压缩函数，通过asyncio.to_thread在异步环境中运行
        await asyncio.to_thread(
            _create_zip_sync,
            all_files,
            str(docs_dir),
            str(zip_file_path)
        )
        olog.info("文件压缩完成")

        olog.info("开始上传压缩文件到OSS")
        oss_client = OSSClient()

        suffix = zip_file_path.suffix
        rand_part = "".join(random.choices(string.ascii_letters + string.digits, k=6))
        oss_key = f"{SCA_DOCS_OSS_KEY}/{state.requirement_outline['project_name']}_{rand_part}{suffix}"
        print(oss_key)
        await oss_client.upload_file(oss_key, str(zip_file_path))

        olog.info(f"{compress_and_upload.__name__} 节点执行完成")
        return {"oss_key": oss_key}
    finally:
        # 异步清理临时压缩文件
        if await zip_file_path.exists():
            await zip_file_path.unlink()
            olog.debug(f"已清理临时压缩文件: {zip_file_path}")
