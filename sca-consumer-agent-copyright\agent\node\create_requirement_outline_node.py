from typing import Dict, Any, List

from pydantic import BaseModel, Field
from omni.llm.output_agent import structured_output_handler
from omni.log.log import olog
from agent.state import OverallState

class PageRequirement(BaseModel):
    """页面需求模型"""
    page_name: str = Field(description="页面名称")
    page_description: str = Field(description="页面功能和需求描述")
    page_usage_instructions: str = Field(description="用户的使用说明，需要详细描述本页面的操作步骤，以及如何从其他相关页面导航到此页面，和从本页面可以导航到哪些其他页面，体现页面在模块流程中的位置。")
    page_path: str = Field(description="页面路径，遵循Next.js的路由规则")


class ModuleRequirement(BaseModel):
    """模块需求模型"""
    module_name: str = Field(description="模块名称")
    module_description: str = Field(description="模块功能和需求描述")
    pages: List[PageRequirement] = Field(description="模块下的页面列表，每个项是PageRequirement对象")
    module_usage_flow_concept: str = Field(description="模块的用户使用步骤关联概念，详细描述模块内部各个页面之间的主要跳转逻辑、操作顺序和数据流转关系。")


class RequirementOutline(BaseModel):
    """需求大纲模型"""
    project_name: str = Field(description="项目名称")
    project_description: str = Field(description="项目描述")
    modules: List[ModuleRequirement] = Field(description="项目模块列表，每个项是ModuleRequirement对象")

# 提示模板
OUTLINE_PROMPT = """
# 角色
你是一名专业的前端UI/UX设计师和产品经理。

# 背景
用户需求：{user_requirement}

# 任务
请分析这个需求，生成一个完整的软件UI设计，包括：
1. 项目名称
2. 项目描述
3. 模块列表:包含每个模块的名称、描述和用户使用步骤关联的概念。
4. 每个模块下的页面列表:包含每个页面的名称，功能和需求描述，用户使用说明，访问路径

# 约束
项目名称请使用中文
所有页面路径都应以/public为根路径
项目必须包含一个首页,首页应该是所有功能的起点和导航中心,首页是必须存在的,首页的page_path是'/public/home'
项目必须包含一个登录页,登录页是必须存在的,登录页的page_path是'/public/login'
对于需要使用动态路由的页面，page_path应该采用Next.js的动态路由规则，例如：'/public/module/[id]'，其中方括号内的部分表示动态参数（如：'/public/tasks/[taskId]'、'/public/products/[productId]/edit'等）
绝对不要生成不同名称的相同动态路径参数文件,错误示范:/app/raffle/[raffleId]/page.tsx与/app/raffle/[resultId]/page.tsx
生成的模块总数必须大于 5 个
页面请拆分到最细粒度,如新增页面,修改页面,列表页面,详情页面,等等
"""

async def create_requirement_outline(state: OverallState) -> Dict[str, Any]:
    """需求大纲生成节点函数"""
    olog.info("开始执行需求大纲生成节点")
    
    params = {"user_requirement": state.user_requirement}
    
    for i in range(10):
        temp_result = await structured_output_handler(
            prompt_template=OUTLINE_PROMPT,
            params=params,
            output_model=RequirementOutline
        )
        
        # 检查必需页面是否存在
        all_pages = [p for m in temp_result.modules for p in m.pages]
        has_home = any(p.page_path.strip() == "/public/home" for p in all_pages)
        has_login = any(p.page_path.strip() == "/public/login" for p in all_pages)
        
        if has_home and has_login:
            # 标准化页面路径
            for module in temp_result.modules:
                for page in module.pages:
                    if not page.page_path.startswith("/public"):
                        if page.page_path.startswith("/"):
                            page.page_path = "/public" + page.page_path
                        else:
                            page.page_path = "/public/" + page.page_path
            
            olog.info("需求大纲生成成功")
            return {"requirement_outline": temp_result.model_dump()}
    
    olog.error("已达到最大重试次数，未能生成有效需求大纲")
    raise ValueError("无法生成包含首页和登录页的完整需求大纲")