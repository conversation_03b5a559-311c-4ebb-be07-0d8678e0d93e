import asyncio
import os
import tarfile
import tempfile
from typing import Dict, Any, List

import asyncssh
import aiofiles.tempfile
from playwright.async_api import async_playwright
from aiopath import AsyncPath

from agent.state import OverallState
from config.config import REMOTE_CONFIG
from omni.log.log import olog
from omni.redis.machine_semaphore_lock import MachineSemaphoreLock
from agent.utils.path_utils import smart_join_path

# 常量定义
PAGE_TIMEOUT = 30000
SCREENSHOT_TIMEOUT = 15000
SCREENSHOT_VIEWPORT = {"width": 1920, "height": 1080}


def _create_tar_archive_sync(tar_path: str, source_dir: str) -> None:
    """
    同步创建tar压缩包的辅助函数

    Args:
        tar_path: 压缩包输出路径
        source_dir: 源目录路径
    """
    with tarfile.open(tar_path, 'w:gz') as tar:
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, source_dir)
                tar.add(file_path, arcname=arcname)


async def deploy_and_check(state: OverallState) -> Dict[str, Any]:
    """部署和检查节点"""
    olog.info("开始执行部署和检查流程")
    
    async with MachineSemaphoreLock(max_concurrent=1, timeout=600, lock_name="deploy_and_check"):
        # 1. 远程部署
        olog.info("开始远程部署")
        remote_dir = REMOTE_CONFIG["remote_dir"]
        
        conn = None
        try:
            conn = await asyncssh.connect(
                host=REMOTE_CONFIG["host"],
                port=REMOTE_CONFIG["port"], 
                username=REMOTE_CONFIG["username"],
                password=REMOTE_CONFIG["password"],
                known_hosts=None
            )
            olog.info("SSH连接成功，清理远程目录")
            await conn.run(f"rm -rf {remote_dir}/*")
            
            # 创建并上传项目压缩包
            temp_path = None
            try:
                # 异步创建临时文件
                temp_file = await aiofiles.tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False)
                temp_path = temp_file.name
                await temp_file.close()

                olog.debug("开始打包项目文件")
                # 在线程池中异步创建项目压缩包
                await asyncio.to_thread(_create_tar_archive_sync, temp_path, state.project_dir)
                
                olog.debug("开始上传文件")
                async with conn.start_sftp_client() as sftp:
                    await sftp.put(temp_path, f"{remote_dir}/project.tar.gz")
                olog.debug("文件上传完成")
                
            finally:
                # 异步清理临时文件
                if temp_path:
                    temp_async_path = AsyncPath(temp_path)
                    if await temp_async_path.exists():
                        await temp_async_path.unlink()
            
            olog.debug("解压文件并重启服务")
            await conn.run(f"cd {remote_dir} && tar -xzf project.tar.gz && rm project.tar.gz")
            await conn.run("docker restart sca-web-sandbox")
            
        except Exception as e:
            olog.error(f"远程部署过程中发生错误: {str(e)}")
            raise
        finally:
            # 确保SSH连接总是被关闭
            if conn:
                conn.close()
        
        await asyncio.sleep(5)
        olog.info("远程部署完成")
        
        # 2. 页面检查
        new_attempts = state.page_check_attempts + 1
        # 提取所有页面路径
        page_paths = [page['page_path'] for module in state.requirement_outline['modules'] for page in module['pages']]
        base_url = REMOTE_CONFIG["remote_base_url"]
        error_pages = {}
        
        olog.info(f"开始全量检查 {len(page_paths)} 个页面")
        
        async with async_playwright() as playwright:
            browser = await playwright.chromium.launch(headless=True)
            
            for page_path in page_paths:
                page_url = f"{base_url.rstrip('/')}{page_path}"
                olog.debug(f"检查页面: {page_path}")
                
                errors = []
                page = await browser.new_page()
                
                page.on("pageerror", lambda exception: errors.append(f"Exception: {str(exception)}"))
                
                response = await page.goto(page_url, timeout=PAGE_TIMEOUT)
                if response.status >= 400:
                    errors.append(f"HTTP {response.status}: {response.status_text}")
                await asyncio.sleep(1)
                await page.close()
                
                if errors:
                    error_pages[page_path] = errors
                    olog.warning(f"页面 {page_url} 检测到 {len(errors)} 个错误")
                else:
                    olog.debug(f"页面 {page_path} 检查通过")
            
            await browser.close()
        
        olog.info(f"全量检查完成 - 错误页面: {len(error_pages)}/{len(page_paths)}")
        
        # 3. 截图处理
        screenshot_results = {}
        if not error_pages:
            olog.info("所有页面检查通过，开始截图")
            
            async with async_playwright() as playwright:
                browser = await playwright.chromium.launch(headless=True)
                
                for page_path in page_paths:
                    page_url = f"{base_url.rstrip('/')}{page_path}"
                    
                    # 查找页面信息
                    page_info = next(
                        (page for module in state.requirement_outline['modules'] 
                         for page in module['pages'] if page['page_path'] == page_path),
                        None
                    )
                    if not page_info:
                        continue
                        
                    # 生成截图路径
                    page_name = page_info['page_name']
                    safe_name = "".join(c if c.isalnum() or c in '-_' else "_" for c in page_name)
                    screenshot_path = smart_join_path(state.images_dir, f"{safe_name}.png")
                    
                    page = await browser.new_page(viewport=SCREENSHOT_VIEWPORT)
                    page.set_default_timeout(SCREENSHOT_TIMEOUT)
                    
                    
                    await page.goto(page_url)
                    await page.wait_for_selector("body", timeout=5000)
                    await asyncio.sleep(1)
                    
                    await AsyncPath(screenshot_path).parent.mkdir(parents=True, exist_ok=True)
                    await page.screenshot(path=screenshot_path, full_page=True)
                    await page.close()
                    
                    olog.debug(f"截图成功: {page_name}")
                    screenshot_results[page_path] = screenshot_path
                
                await browser.close()
                olog.debug(f"截图完成 - 成功: {len(screenshot_results)} 页")
        
        # 4. 返回结果
        result = {
            "page_check_attempts": new_attempts,
            "has_page_errors": bool(error_pages),
            "page_error_mapping": error_pages,
            "screenshot_results": screenshot_results
        }
        
        olog.info(f"部署和检查流程完成 - 错误: {len(error_pages)}页, 截图: {len(screenshot_results)}页")
        olog.info("部署和检查节点执行完成")
        return result