import json
import os
from typing import Dict, Any, Optional

import aiofiles
from pydantic import BaseModel, Field
from omni.llm.output_agent import structured_output_handler
from omni.log.log import olog
from agent.state import OverallState
from agent.utils.path_utils import smart_join_path


# node3_1模型 - 软件著作权申请文档生成节点
class SoftwareCopyrightInfo(BaseModel):
    """软件著作权申请所需的信息模型"""
    software_name: str = Field(description="给定的软件全称")
    version: str = Field(description="软件版本号，如V1.0，需与后续提交材料保持一致")
    short_name: Optional[str] = Field(description="根据软件全称生成的软件简称")
    software_category: str = Field(description="软件分类，如应用软件、系统软件等")
    dev_hardware_env: str = Field(description="软件开发的硬件环境，包括CPU、内存、硬盘等具体配置")
    run_hardware_env: str = Field(description="软件运行的硬件环境，包括CPU、内存、硬盘等具体配置")
    dev_os: str = Field(description="开发该软件的操作系统名称及版本")
    dev_tools: str = Field(description="软件开发环境/开发工具名称及版本")
    run_platform: str = Field(description="该软件的运行平台/操作系统名称及版本")
    supporting_software: str = Field(description="软件运行支撑环境/支持软件名称及版本")
    programming_language: str = Field(description="编程语言及版本号，如JavaScript ES6、TypeScript 4.9等")
    source_code_amount: str = Field(description="软件代码行数，即软件源代码总行数")
    dev_purpose: str = Field(description="开发目的")
    target_industry: str = Field(description="面向领域/行业")
    main_functions: str = Field(description="软件的主要功能，详细描述")
    technical_features: str = Field(description="软件技术特点，简短明确描述软件的主要功能和技术特点，限500字以内")
    special_features: str = Field(description="特点选项，如大数据软件、云计算软件等")

"""
软件著作权申请文档生成智能体节点
"""

PROMPT = """
请根据以下项目信息，生成一份完整的软件著作权申请文档内容：

项目详细信息: 
{requirement_outline_json}

请注意：
1. software_name (软件全称) 由用户在项目信息中直接提供，请直接使用该名称。
2. 请根据 software_name 生成一个合适的 short_name (软件简称)。
3. 前端技术栈固定为 NextJS、React 和 TailWindCSS，无论项目实际使用何种技术
4. 在生成编程语言、软件开发环境/开发工具等描述时，必须包含这些前端技术
5. 软件全称应当由公司简称/品牌+产品用途与功能+"软件"结尾，例如"森林知产管理平台软件" (此条针对用户提供的software_name，AI无需再生成)
6. 软件技术特点需要简短明确，限500字以内
7. 软件开发、运行环境需包含CPU、内存、硬盘等硬件要求和操作系统及支持软件名称和版本
8. 编程语言需要包含版本号
9. 软件的主要功能描述，需要超过100字

"""


async def generate_apply_doc(state: OverallState) -> Dict[str, Any]:
    """
    软件著作权申请文档生成节点函数（对应文件: generate_apply_doc_node.py）
    
    Args:
        state: 整体状态对象
        
    Returns:
        Dict[str, Any]: 状态更新字典
    """
    requirement_outline_json = json.dumps(state.requirement_outline, ensure_ascii=False, indent=2)
    docs_dir = state.docs_dir
    olog.info(f"开始执行 {generate_apply_doc.__name__} 节点，文档目录: {docs_dir}")

    params = {"requirement_outline_json": requirement_outline_json}
    copyright_info = await structured_output_handler(
        prompt_template=PROMPT,
        params=params,
        output_model=SoftwareCopyrightInfo
    )
    olog.info("LLM生成软著申请内容完成")

    # 创建软件著作权申请TXT文档
    olog.info("开始创建软件著作权申请TXT文档")

    # 创建TXT文档
    doc_filename = f"{copyright_info.software_name}-申请书.txt"
    copyright_doc_path = smart_join_path(docs_dir, doc_filename)

    # 构建文档内容
    content_items = [
        ("【软件全称】：", copyright_info.software_name),
        ("【版本号】：", copyright_info.version),
        ("【软件简称】：", copyright_info.short_name),
        ("【软件分类】：", copyright_info.software_category),
        ("【软件开发的硬件环境】：", copyright_info.dev_hardware_env),
        ("【软件运行的硬件环境】：", copyright_info.run_hardware_env),
        ("【软件开发的操作系统】：", copyright_info.dev_os),
        ("【软件开发环境/开发工具】：", copyright_info.dev_tools),
        ("【软件运行的操作系统】：", copyright_info.run_platform),
        ("【软件运行支撑环境/支持软件】：", copyright_info.supporting_software),
        ("【编程语言及版本号】：", copyright_info.programming_language),
        ("【软件代码行数】：", copyright_info.source_code_amount),
        ("【开发目的】：", copyright_info.dev_purpose),
        ("【面向领域/行业】：", copyright_info.target_industry),
        ("【软件的主要功能】：", copyright_info.main_functions),
        ("【软件技术特点】：", copyright_info.technical_features),
        ("【特点选项】：", copyright_info.special_features)
    ]

    content = ['软件著作权申请文档', '']
    for title, text in content_items:
        content.extend([title, text, ''])

    async with aiofiles.open(copyright_doc_path, "w", encoding="utf-8") as f:
        await f.write("\n".join(content))

    olog.info(f"软件著作权申请TXT文档已保存到: {copyright_doc_path}")

    copyright_apply_str = json.dumps(copyright_info.model_dump(), ensure_ascii=False, indent=4)
    olog.info(f"{generate_apply_doc.__name__} 节点执行完成")

    return {
        "copyright_apply_doc_path": copyright_doc_path,
        "copyright_apply_str": copyright_apply_str
    }