import asyncio
import os
from pathlib import Path
from typing import Dict, Any

import aiofiles
from aiopath import AsyncPath

from omni.log.log import olog
from agent.state import OverallState
from agent.utils.path_utils import smart_join_path
from agent.utils.docx_utils import WordCodeDocumentGenerator

"""
代码文档生成智能体节点
生成符合软著要求的代码文档：全量代码，每页50行代码
"""


async def generate_code_doc(state: OverallState) -> Dict[str, Any]:
    """
    代码文档生成节点函数（对应文件: generate_code_doc_node.py）
    
    Args:
        state: 整体状态对象
        
    Returns:
        Dict[str, Any]: 状态更新字典
    """
    olog.info(f"开始执行 {generate_code_doc.__name__} 节点")

    # 获取必要参数
    project_name = state.requirement_outline["project_name"]
    project_dir = state.project_dir
    pages = state.pages
    docs_dir = state.docs_dir

    # 确保文档目录存在
    await AsyncPath(docs_dir).mkdir(parents=True, exist_ok=True)

    # 设置软件信息和Word文件
    software_name = project_name if project_name else "软件"
    software_version = "v1.0"
    docx_filename = f"{software_name}-代码文档.docx"
    docx_path = smart_join_path(docs_dir, docx_filename)
    
    # 1. 收集所有代码行
    all_lines = []
    if pages:
        for page in pages:
            page_path = page.get("page_path", "")
            if not page_path:
                continue

            # 构建文件路径
            file_path = smart_join_path(project_dir, page_path, "page.tsx")
            async_file_path = AsyncPath(file_path)

            if not await async_file_path.exists():
                olog.warning(f"文件不存在: {file_path}")
                continue

            # 读取并处理代码
            async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                content = await f.read()
                lines = content.split("\n")
            all_lines.extend(lines)
            olog.debug(f"已处理文件: {file_path}, 行数: {len(lines)}")

    total_lines = len(all_lines)
    olog.debug(f"已收集所有文件的代码，总计{total_lines}行")
    olog.info(f"将使用全量代码，总计 {total_lines} 行")

    # 2. 使用新的WordCodeDocumentGenerator生成文档
    code_doc_generator = WordCodeDocumentGenerator()
    await code_doc_generator.generate_code_document(all_lines, docx_path, software_name, software_version)

    olog.info(f"{generate_code_doc.__name__} 节点执行完成")
    return {"code_doc_path": docx_path}