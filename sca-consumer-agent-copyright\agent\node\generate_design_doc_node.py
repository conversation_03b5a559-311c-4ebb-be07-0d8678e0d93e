import asyncio
import json
import os
from typing import Dict, Optional, List, Any, Coroutine

import aiofiles
from aiopath import AsyncPath
import docx
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import Pt, RGBColor, Inches
from pydantic import BaseModel, Field

from omni.llm.output_agent import structured_output_handler
from omni.log.log import olog
from agent.state import OverallState
from agent.utils.path_utils import smart_join_path
from agent.utils.docx_utils import WordDocumentGenerator, WordHeaderFooterGenerator
from agent.utils.plant_uml_utils import generate_plant_uml, PlantUMLBadRequestError


# ==================== 数据模型区域 ====================
# Pydantic数据模型定义，用于结构化设计说明书各个章节的内容

# node3_4模型 - 设计说明书生成节点
class IntroductionSection(BaseModel):
    """引言部分的内容模型"""
    background: str = Field(description="背景介绍：简要说明软件开发的背景和目的")
    purpose: str = Field(description="编写目的：明确设计说明书的编写目的和适用范围")


class ProjectOverviewSection(BaseModel):
    """项目概述部分的内容模型"""
    project_name: str = Field(description="项目名称：软件项目的正式名称")
    project_description: str = Field(description="项目描述：简要介绍软件的功能和特点")
    target_users: str = Field(description="目标用户：软件的目标用户群体")
    application_field: str = Field(description="应用领域：软件适用的行业或领域")


class TechnicalArchitectureSection(BaseModel):
    """技术架构设计部分的内容模型"""
    system_architecture: str = Field(description="系统架构：描述软件的整体架构")
    programming_language: str = Field(description="编程语言：使用的编程语言及版本")
    development_framework: str = Field(description="开发框架：使用的开发框架及版本")
    database_technology: str = Field(description="数据库技术：使用的数据库类型及版本")
    runtime_environment: str = Field(description="运行环境：软件运行所需的环境配置")


class DatabaseDesignSection(BaseModel):
    """数据库设计部分的内容模型"""
    database_structure: str = Field(description="数据库结构：包括表结构设计、字段定义等逻辑结构说明")


class ModuleDesignSection(BaseModel):
    """功能模块设计部分的内容模型"""
    module_division: str = Field(description="模块划分：详细说明软件系统的模块划分，明确每个模块的职责")
    module_functions: str = Field(description="模块功能：具体描述每个模块的功能实现方式")
    module_relationships: str = Field(description="模块间关系：说明模块之间的调用关系和数据交互方式")


class AlgorithmDesignSection(BaseModel):
    """算法设计部分的内容模型"""
    algorithms: str = Field(description="详细说明软件中关键算法的原理、实现过程及其优势")


class SecurityPerformanceSection(BaseModel):
    """安全设计与性能优化部分的内容模型"""
    security_design: str = Field(description="安全机制设计：软件的安全保障措施")
    performance_optimization: str = Field(description="性能优化措施：提升软件性能的设计和实现")


class RuntimeEnvironmentSection(BaseModel):
    """运行环境和兼容性部分的内容模型"""
    operating_system: str = Field(description="操作系统要求：软件运行所需的操作系统类型和版本")
    hardware_requirements: str = Field(description="硬件配置：软件运行所需的硬件最低配置")
    dependent_software: str = Field(description="依赖软件：软件运行所依赖的其他软件")
    compatibility: str = Field(description="兼容性：软件在不同环境下的兼容性和性能表现")


class NewDesignDocument(BaseModel):
    """新版软著设计说明书的内容模型"""
    introduction: IntroductionSection = Field(description="引言")
    project_overview: ProjectOverviewSection = Field(description="项目概述")
    technical_architecture: TechnicalArchitectureSection = Field(description="技术架构设计")
    database_design: DatabaseDesignSection = Field(description="数据库设计")
    module_design: ModuleDesignSection = Field(description="功能模块设计")
    algorithm_design: AlgorithmDesignSection = Field(description="算法设计")
    security_performance: SecurityPerformanceSection = Field(description="安全设计与性能优化")
    runtime_environment: RuntimeEnvironmentSection = Field(description="运行环境和兼容性")


# ==================== 配置区域 ====================
# 文档生成相关的配置常量和设置已移动到 agent.utils.docx_utils


# UML图表模型定义
class ClassDiagramModel(BaseModel):
    """类图UML代码模型"""
    uml_code: str = Field(description="完整的PlantUML类图代码，包含@startuml和@enduml标记")


class ERDiagramModel(BaseModel):
    """ER图UML代码模型"""
    uml_code: str = Field(description="完整的PlantUML ER图代码，包含@startuml和@enduml标记")


# ==================== 独立函数区域 ====================
# 包含 structured_output_handler 调用的函数

async def _generate_uml_diagram(
    requirement_outline_json: str,
    images_dir: str,
    template_path: str,
    output_filename: str,
    output_model: type[BaseModel]
) -> dict | None:
    """通用UML图生成函数，包含AI重新生成的重试机制"""
    prompt_template = f"""
# 角色：
你是一名专业的软件工程师和UML专家。

# 背景：
{{requirement_outline_json}}

# 任务：
根据上述项目信息，生成一个完整的PlantUML代码。

# 参考示例：
```
{{uml_example}}
```

# 返回值：
请严格按照以下Pydantic模型的JSON格式输出，不要添加任何额外的解释或说明：
"""
    
    await AsyncPath(images_dir).mkdir(parents=True, exist_ok=True)

    uml_template_path = smart_join_path(os.getcwd(), template_path)
    async with aiofiles.open(uml_template_path, 'r', encoding='utf-8') as f:
        uml_example = await f.read()

    params = {
        "requirement_outline_json": requirement_outline_json,
        "uml_example": uml_example,
    }

    output_path = smart_join_path(images_dir, output_filename)
    max_retries = 20
    
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                olog.info(f"UML图生成第 {attempt + 1} 次尝试: {output_filename}")
            
            # AI 生成 UML 代码
            result = await structured_output_handler(
                prompt_template=prompt_template,
                params=params,
                output_model=output_model
            )
            
            # 尝试转换为图片
            return await generate_plant_uml(result.uml_code, output_path)
            
        except PlantUMLBadRequestError as e:
            olog.warning(f"UML图生成失败 (第 {attempt + 1}/{max_retries} 次): {e}")
            if attempt == max_retries - 1:
                olog.error(f"UML图生成达到最大重试次数 ({max_retries})，生成失败: {output_filename}")
                raise
            # 继续下一次重试，AI会重新生成不同的UML代码
        except Exception as e:
            olog.error(f"UML图生成遇到其他错误: {e}")
            raise


async def _generate_section_content(
    section_name: str,
    pydantic_model_class: type[BaseModel],
    requirement_outline_json: str,
    copyright_apply_str: str
) -> BaseModel:
    """使用LLM为设计说明书的特定部分生成内容"""
    olog.debug(f"开始为 '{section_name}' 部分生成内容...")

    prompt_template = """
# 角色：
你是一名专业的软件工程师。

# 背景：
你正在编写软件设计说明书。
该软件的相关信息如下：
- 需求大纲 (JSON格式): {requirement_outline_json}
- 软件著作权申请信息: {copyright_apply_str}

# 任务：
请为软件设计说明书的 "{section_name_zh}" 章节生成内容。

# 约束：
- 内容必须专业、详细。
- 内容必须符合软件著作权申请的要求。
- 不要出现markdown格式。

# 返回值：
请严格按照以下Pydantic模型的JSON格式输出，不要添加任何额外的解释或说明：
"""

    params = {
        "requirement_outline_json": requirement_outline_json,
        "copyright_apply_str": copyright_apply_str,
        "section_name_zh": section_name,
    }

    section_content = await structured_output_handler(
        prompt_template=prompt_template,
        params=params,
        output_model=pydantic_model_class
    )
    olog.debug(f"'{section_name}' 部分内容生成完成")
    return section_content

# ==================== 主函数区域 ====================
# 设计说明书生成节点主函数

async def generate_design_doc(state: OverallState) -> Dict[str, Any]:
    """设计说明书生成节点函数"""
    olog.info(f"开始执行 {generate_design_doc.__name__} 节点")

    # 提取参数
    requirement_outline_json = json.dumps(state.requirement_outline, ensure_ascii=False, indent=2)
    copyright_apply_str = state.copyright_apply_str
    images_dir = state.images_dir
    docs_dir = state.docs_dir

    # 准备生成数据
    requirement_outline = json.loads(requirement_outline_json)
    project_name = requirement_outline["project_name"]
    # 软件版本现在由 WordHeaderFooterGenerator 管理

    # 并发执行所有任务
    olog.info("开始并发生成图表和章节内容...")
    
    # 创建所有异步任务
    tasks = {
        "class_diagram_path": _generate_uml_diagram(
            requirement_outline_json=requirement_outline_json,
            images_dir=images_dir,
            template_path="resource/uml_template/uml.puml",
            output_filename="class_diagram.png",
            output_model=ClassDiagramModel
        ),
        "er_diagram_path": _generate_uml_diagram(
            requirement_outline_json=requirement_outline_json,
            images_dir=images_dir,
            template_path="resource/uml_template/er.puml",
            output_filename="er_diagram.png",
            output_model=ERDiagramModel
        ),
        "introduction": _generate_section_content(
            "引言", IntroductionSection, 
            requirement_outline_json, copyright_apply_str
        ),
        "project_overview": _generate_section_content(
            "项目概述", ProjectOverviewSection,
            requirement_outline_json, copyright_apply_str
        ),
        "technical_architecture": _generate_section_content(
            "技术架构设计", TechnicalArchitectureSection,
            requirement_outline_json, copyright_apply_str
        ),
        "database_design": _generate_section_content(
            "数据库设计", DatabaseDesignSection,
            requirement_outline_json, copyright_apply_str
        ),
        "module_design": _generate_section_content(
            "功能模块设计", ModuleDesignSection,
            requirement_outline_json, copyright_apply_str
        ),
        "algorithm_design": _generate_section_content(
            "算法设计", AlgorithmDesignSection,
            requirement_outline_json, copyright_apply_str
        ),
        "security_performance": _generate_section_content(
            "安全设计与性能优化", SecurityPerformanceSection,
            requirement_outline_json, copyright_apply_str
        ),
        "runtime_environment": _generate_section_content(
            "运行环境和兼容性", RuntimeEnvironmentSection,
            requirement_outline_json, copyright_apply_str
        )
    }
    
    # 并发执行所有任务
    results = await asyncio.gather(*tasks.values())
    results = dict(zip(tasks.keys(), results))

    # ==================== 5. 构建文档内容 ====================
    olog.info("所有内容生成完成，开始组织Word文档...")
    
    # 构建文档结构
    document_content = {
        "title": f"{project_name}软件设计说明书",
        "sections": [
            {
                "heading": "1. 引言",
                "content": {
                    "背景介绍": results["introduction"].background,
                    "编写目的": results["introduction"].purpose
                }
            },
            {
                "heading": "2. 项目概述", 
                "content": {
                    "项目名称": results["project_overview"].project_name,
                    "项目描述": results["project_overview"].project_description,
                    "目标用户": results["project_overview"].target_users,
                    "应用领域": results["project_overview"].application_field
                }
            },
            {
                "heading": "3. 技术架构设计",
                "content": {
                    "系统架构": results["technical_architecture"].system_architecture,
                    "技术架构图": results["class_diagram_path"],
                    "编程语言": results["technical_architecture"].programming_language,
                    "开发框架": results["technical_architecture"].development_framework,
                    "数据库技术": results["technical_architecture"].database_technology,
                    "运行环境": results["technical_architecture"].runtime_environment
                }
            },
            {
                "heading": "4. 功能模块设计",
                "content": {
                    "模块划分": results["module_design"].module_division,
                    "模块功能": results["module_design"].module_functions,
                    "模块间关系": results["module_design"].module_relationships
                }
            },
            {
                "heading": "5. 数据库设计",
                "content": {
                    "数据库结构": results["database_design"].database_structure,
                    "E-R图": results["er_diagram_path"]
                }
            },
            {
                "heading": "6. 算法设计",
                "content": results["algorithm_design"].algorithms
            },
            {
                "heading": "7. 安全设计与性能优化",
                "content": {
                    "安全机制设计": results["security_performance"].security_design,
                    "性能优化措施": results["security_performance"].performance_optimization
                }
            },
            {
                "heading": "8. 运行环境和兼容性",
                "content": {
                    "操作系统要求": results["runtime_environment"].operating_system,
                    "硬件配置": results["runtime_environment"].hardware_requirements,
                    "依赖软件": results["runtime_environment"].dependent_software,
                    "兼容性": results["runtime_environment"].compatibility
                }
            }
        ]
    }

    # ==================== 6. 生成Word文档 ====================
    filename = f"{project_name}-软件设计说明书.docx"
    olog.debug(f"开始生成Word文档: {filename}")

    # 创建输出目录和文档对象
    await AsyncPath(docs_dir).mkdir(parents=True, exist_ok=True)
    doc = docx.Document()
    generator = WordDocumentGenerator(doc)
    
    # 设置页眉页码
    header_footer_generator = WordHeaderFooterGenerator()
    header_footer_generator.setup_header_footer(doc, project_name)

    # 1. 创建封面页
    olog.debug("步骤1: 创建封面页")
    generator.create_cover_page(project_name, "软件设计说明书", "V1.0")
    
    # 2. 创建文档信息表格
    olog.debug("步骤2: 创建文档信息表格") 
    generator.create_document_info_table()
    
    # 3. 创建修订历史表格
    olog.debug("步骤3: 创建修订历史表格")
    revision_data = [
        {"version": "V0.1", "description": "创建系统架构和基础设计"},
        {"version": "V0.2", "description": "完善模块设计和数据库设计"},
        {"version": "V1.0", "description": "完成算法设计和性能优化说明"}
    ]
    generator.create_revision_history_table(revision_data)
    
    # 4. 添加分页符
    olog.debug("步骤4: 添加分页符")
    generator.add_page_break()

    # 添加文档标题
    generator.add_heading(
        document_content["title"], 
        level=0, 
        size=generator.title_font_size, 
        bold=True, 
        color=RGBColor(0, 0, 0)
    )

    # 添加所有章节内容
    for section in document_content["sections"]:
        generator.add_heading(
            section["heading"], 
            level=1, 
            size=generator.section_font_size, 
            bold=True
        )
        await generator.process_content(section["content"])

    # 保存文档
    output_path = smart_join_path(docs_dir, filename)
    await asyncio.to_thread(doc.save, output_path)
    olog.debug(f"Word文档生成完成: {output_path}")

    olog.info(f"{generate_design_doc.__name__} 节点执行完成")
    return {"design_doc_path": output_path}