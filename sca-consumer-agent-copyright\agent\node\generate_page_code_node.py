import asyncio
import os
from pathlib import Path
from typing import Dict, Any

import aiofiles
from aiopath import AsyncPath
from pydantic import BaseModel, Field
from omni.llm.output_agent import structured_output_handler
from omni.log.log import olog
from agent.state import OverallState
from agent.utils.path_utils import smart_join_path


class PageCodeResult(BaseModel):
    """页面代码生成结果模型"""
    code_content: str = Field(description="页面代码内容")

"""
界面代码生成智能体节点
"""


async def generate_single_page_code(
        page_requirement: dict,
        requirement_outline_json: str,
        project_dir: str
) -> Dict[str, Any]:
    """为单个页面生成界面代码和文件路径

    Args:
        page_requirement: 页面需求字典，包含page_name、page_description、detailed_requirements、user_interactions等字段
        requirement_outline_json: 包含项目信息（如 project_name, platform_type）和Node1返回的完整需求大纲信息
        project_dir: 沙箱项目根目录路径
        
    Returns:
        Dict[str, Any]: 包含页面代码信息的字典
    """
    olog.info(f"开始生成页面代码: {page_requirement['page_name']}")

    page_code_prompt = """
# 角色
你是一名专业的前端开发工程师

# 项目整体需求
```json
{requirement_outline}
```

# 当前页面需求
页面名称：{page_name}
页面功能描述：{page_description}
用户使用说明：{page_usage_instructions}

# 前端开发规范

## 一、风格约束

### 设计要求

- **UI和UX风格**：完全遵循Google Material Design设计语言，采用现代化的科技感设计风格，整体视觉和交互体验符合Google生态标准。

### 设计核心原则

- **质感设计（Material Design）**：运用纸质隐喻，通过阴影、层次、动效营造真实感。
- **无障碍友好**：遵循WCAG标准，确保可访问性。

### 视觉与交互细节

- 现代化、简洁、注重功能性
- 统一的圆角设计（0.75rem基础圆角）
- Google科技配色体系（已在globals.css中定义）
- 流畅的过渡动效和交互反馈

### 参考标准

- 请参考Google Material Design官方指南，确保产品在美观性、功能性和一致性上达到Google官方标准。

---

## 二、技术约束

### 核心技术栈
- **项目基础**：shadcn/ui + Tailwind CSS + Next.js 15 + TypeScript
- **客户端渲染**：默认采用客户端渲染，需在文件开头添加 `"use client"` 指令

### UI组件库
- **主要组件库**：shadcn/ui,其导入方式@/components/ui/xxx;
- **图标库**：lucide-react（提供丰富的图标选择）
- **图表库**：recharts（用于数据可视化和图表展示）

### 路径引入规范
- 所有路径引入需要使用相对路径或绝对路径
- 组件导入优先使用 `@/` 别名（@/components/ui/xxx';）

## 三、使用到的库

```
"@dnd-kit/core": "^6.3.1",
"@dnd-kit/modifiers": "^9.0.0",
"@dnd-kit/sortable": "^10.0.0",
"@dnd-kit/utilities": "^3.2.2",
"@hookform/resolvers": "^5.2.0",
"@radix-ui/react-accordion": "^1.2.11",
"@radix-ui/react-alert-dialog": "^1.1.14",
"@radix-ui/react-aspect-ratio": "^1.1.7",
"@radix-ui/react-avatar": "^1.1.10",
"@radix-ui/react-checkbox": "^1.3.2",
"@radix-ui/react-collapsible": "^1.1.11",
"@radix-ui/react-context-menu": "^2.2.15",
"@radix-ui/react-dialog": "^1.1.14",
"@radix-ui/react-dropdown-menu": "^2.1.15",
"@radix-ui/react-hover-card": "^1.1.14",
"@radix-ui/react-label": "^2.1.7",
"@radix-ui/react-menubar": "^1.1.15",
"@radix-ui/react-navigation-menu": "^1.2.13",
"@radix-ui/react-popover": "^1.1.14",
"@radix-ui/react-progress": "^1.1.7",
"@radix-ui/react-radio-group": "^1.3.7",
"@radix-ui/react-scroll-area": "^1.2.9",
"@radix-ui/react-select": "^2.2.5",
"@radix-ui/react-separator": "^1.1.7",
"@radix-ui/react-slider": "^1.3.5",
"@radix-ui/react-slot": "^1.2.3",
"@radix-ui/react-switch": "^1.2.5",
"@radix-ui/react-tabs": "^1.1.12",
"@radix-ui/react-toggle": "^1.1.9",
"@radix-ui/react-toggle-group": "^1.1.10",
"@radix-ui/react-tooltip": "^1.2.7",
"@tabler/icons-react": "^3.34.1",
"@tanstack/react-table": "^8.21.3",
"class-variance-authority": "^0.7.1",
"clsx": "^2.1.1",
"cmdk": "^1.1.1",
"date-fns": "^4.1.0",
"embla-carousel-react": "^8.6.0",
"input-otp": "^1.4.2",
"lucide-react": "^0.525.0",
"next": "15.4.2",
"next-themes": "^0.4.6",
"react": "19.1.0",
"react-day-picker": "^9.8.1",
"react-dom": "19.1.0",
"react-hook-form": "^7.61.1",
"react-resizable-panels": "^3.0.3",
"recharts": "^2.15.4",
"sonner": "^2.0.6",
"tailwind-merge": "^3.3.1",
"vaul": "^1.1.2",
"zod": "^4.0.11"
```

# 任务
根据项目整体需求,当前页面需求,前端开发规范,生成完整的PC端页面代码

# 约束
不需要做数据验证
变量名必须使用英文，不能包含中文字符
代码必须严格遵循TypeScript语法规范，所有类型定义正确，确保能够通过TypeScript编译
只能使用lucide-react库中实际存在的图标组件，使用前需验证图标名称的正确性
文本内容中避免使用">"与"<"，防止与JSX语法冲突
asChild属性仅可用于shadcn/ui组件，不可用于原生HTML元素，使用时需正确解构props
Badge组件的variant属性值必须为以下四种之一："default" | "secondary" | "destructive" | "outline"
所有使用 await 的函数必须标记为 async

"""

    olog.debug(f"构建页面 '{page_requirement['page_name']}' 的提示词模板")

    params = {
        "requirement_outline": requirement_outline_json,
        "page_name": page_requirement["page_name"],
        "page_description": page_requirement["page_description"],
        "page_usage_instructions": page_requirement["page_usage_instructions"],
    }

    olog.debug(f"调用LLM生成页面 '{page_requirement['page_name']}' 的代码")
    result = await structured_output_handler(
        prompt_template=page_code_prompt,
        params=params,
        output_model=PageCodeResult,
        llm_name="CODER_LLM",
    )

    # 添加"use client"声明到代码内容开头（如果还没有的话）
    if not result.code_content.strip().startswith('"use client"'):
        client_side_code = "\"use client\";\n" + result.code_content
    else:
        client_side_code = result.code_content

    # 使用智能路径拼接构建文件路径
    file_path = smart_join_path(
        project_dir, page_requirement["page_path"], "page.tsx"
    )

    olog.debug(
        f"成功生成页面代码: {page_requirement['page_name']}, 将保存到: {file_path}"
    )

    # 确保目录存在
    directory = os.path.dirname(file_path)
    await AsyncPath(directory).mkdir(parents=True, exist_ok=True)

    async with aiofiles.open(file_path, "w", encoding="utf-8") as f:
        await f.write(client_side_code)

    olog.info(f"页面 '{page_requirement['page_name']}' 代码生成完成并保存")

    # 返回页面代码信息
    return {
        "page_path": page_requirement["page_path"],
        "code_content": client_side_code
    }


async def generate_page_code(state: OverallState) -> Dict[str, Any]:
    """
    并发生成所有页面的界面代码和文件写入指令（对应文件: generate_page_code_node.py）

    Args:
        state: 整体状态对象
        
    Returns:
        Dict[str, Any]: 状态更新字典
    """
    olog.info(f"开始执行 {generate_page_code.__name__} 节点")

    project_dir = state.project_dir
    
    # 从requirement_outline获取数据并转换为所需格式
    import json
    requirement_outline_json = json.dumps(state.requirement_outline, ensure_ascii=False, indent=2)
    
    # 从requirement_outline中提取所有页面信息
    pages = []
    for module in state.requirement_outline.get('modules', []):
        for page in module.get('pages', []):
            pages.append({
                'page_name': page.get('page_name'),
                'page_description': page.get('page_description'),
                'page_usage_instructions': page.get('page_usage_instructions'),
                'page_path': page.get('page_path')
            })

    olog.debug(f"共有 {len(pages)} 个页面需要处理")
    olog.debug(f"开始并发生成 {len(pages)} 个页面的界面代码")

    # 初始化页面代码字典
    page_code_dict = {}

    # 使用异步并发处理
    tasks = [
        generate_single_page_code(
            page_requirement,
            requirement_outline_json,
            project_dir
        ) for page_requirement in pages
    ]
    
    # 等待所有异步任务完成并收集结果
    page_code_results = await asyncio.gather(*tasks)
    for i, page_code_info in enumerate(page_code_results):
        page_path = pages[i]["page_path"]
        page_code_dict[page_path] = page_code_info["code_content"]

    olog.debug(f"已完成 {len(pages)} 个页面的界面代码生成和文件写入")
    olog.info(f"{generate_page_code.__name__} 节点执行完成")

    return {"page_codes": page_code_dict, "pages": pages}