import asyncio
import json
import os
from typing import Dict, Any, List, Optional

from aiopath import AsyncPath
from docx import Document
from docx.shared import Pt
from pydantic import BaseModel, Field

from omni.llm.output_agent import structured_output_handler
from omni.log.log import olog
from agent.state import OverallState
from agent.utils.path_utils import smart_join_path
from agent.utils.docx_utils import WordDocumentGenerator, WordHeaderFooterGenerator


# node3_3模型 - 使用说明文档生成节点
class ProductBackground(BaseModel):
    """产品背景模型"""
    project_purpose: str = Field(description="项目目的")
    target_users: str = Field(description="目标用户")
    business_value: str = Field(description="业务价值")
    key_features: str = Field(description="主要功能特点")


class ModuleDescription(BaseModel):
    """模块描述模型"""
    module_name: str = Field(description="模块名称")
    module_purpose: str = Field(description="模块目的")
    key_functions: str = Field(description="主要功能点")
    user_workflow: str = Field(description="用户工作流程")


class SinglePageInstructionResult(BaseModel):
    """单个页面使用说明生成结果模型"""
    functional_overview: str = Field(description="页面功能概述")
    operational_steps: List[str] = Field(
        description="操作步骤列表，每个步骤是一段文字描述"
    )
    data_description: str = Field(description="数据说明")


class PageInstructionInfo(BaseModel):
    """页面使用说明信息"""
    page_name: str = Field(description="页面名称")
    page_path: str = Field(description="页面路径")
    image_path: Optional[str] = Field(description="页面截图路径", default=None)
    instructions: SinglePageInstructionResult = Field(description="页面使用说明")


class DocumentContent(BaseModel):
    """文档内容数据"""
    product_background: ProductBackground = Field(description="产品背景")
    modules_description: List[ModuleDescription] = Field(description="模块描述列表")
    page_instructions: List[PageInstructionInfo] = Field(description="页面使用说明列表")

"""
系统使用说明文档生成智能体节点
"""


async def generate_product_background(
        req_outline: str,
) -> ProductBackground:
    """生成产品背景信息

    Args:
        req_outline: 需求大纲JSON字符串

    Returns:
        ProductBackground: 产品背景信息
    """
    olog.info("开始执行产品背景信息生成函数")
    
    prompt_template_str = """
# 角色
你是一名专业的软件产品经理和技术文档专家。

# 背景
以下是一个软件项目的需求大纲：
```json
{req_outline}
```

# 任务
请基于上述需求大纲，生成完整的产品背景信息，包括：
1. 项目目的：描述开发此软件的主要目标和解决的问题
2. 目标用户：描述软件的主要用户群体和他们的特点
3. 业务价值：描述软件带来的价值和效益
4. 主要功能特点：概述软件的主要功能和特色

# 约束
- 输出内容中不要使用任何markdown格式的语法，如标题符号(#)、列表符号(-)、代码块(```)等
- 所有内容应该是纯文本格式
- 不要使用粗体、斜体、表格等任何markdown格式化元素

"""

    params = {"req_outline": req_outline}
    olog.info("正在生成产品背景信息...")
    result = await structured_output_handler(
        prompt_template=prompt_template_str,
        params=params,
        output_model=ProductBackground
    )
    olog.info("产品背景信息生成完成")

    return result


async def generate_single_module_description(
        module: Dict[str, Any],
        req_outline: str,
) -> ModuleDescription:
    """生成单个模块的描述信息

    Args:
        module: 单个模块信息
        req_outline: 需求大纲JSON字符串

    Returns:
        ModuleDescription: 模块描述信息
    """
    module_name = module.get("module_name", "")
    
    prompt_template_str = """
# 角色
你是一名专业的软件架构师和技术文档专家。

# 背景
以下是一个软件项目的需求大纲：
```json
{req_outline}
```

当前需要描述的模块信息：
模块名称：{module_name}
模块描述：{module_description}
模块使用流程概念：{module_usage_flow_concept}
页面列表：{pages_info}

# 任务
请基于上述信息，为该模块生成详细的描述信息，包括：
1. 模块目的：该模块的主要目的和意义
2. 主要功能点：列出该模块提供的主要功能点
3. 用户工作流程：详细描述用户在该模块内的工作流程和操作路径

# 约束
- 输出内容中不要使用任何markdown格式的语法，如标题符号(#)、列表符号(-)、代码块(```)等
- 所有内容应该是纯文本格式
- 不要使用粗体、斜体、表格等任何markdown格式化元素

"""

    module_description = module.get("module_description", "")
    module_usage_flow = module.get("module_usage_flow_concept", "")
    pages = module.get("pages", [])
    
    
    params = {
        "req_outline": req_outline,
        "module_name": module_name,
        "module_description": module_description,
        "module_usage_flow_concept": module_usage_flow,
        "pages_info": json.dumps(pages, ensure_ascii=False),
    }
    
    result = await structured_output_handler(
        prompt_template=prompt_template_str,
        params=params,
        output_model=ModuleDescription
    )
    result.module_name = module_name
    return result


async def generate_single_page_instruction(
        req_outline: str,
        page_data: Dict[str, Any]
) -> Dict[str, Any]:
    """生成单个页面的使用说明

    Args:
        req_outline: 需求大纲JSON字符串
        page_data: 页面信息，包含页面需求和代码信息

    Returns:
        Dict[str, Any]: 包含页面说明的字典
    """
    page_name = page_data.get("page_name", "")
    
    prompt_template_str = """
# 角色
你是一名专业的技术文档编写专家。

# 背景
项目整体需求：
```json
{req_outline}
```

当前页面需求信息：
页面名称：{page_name}
页面功能描述：{page_description}
用户使用说明：{page_usage_instructions}

页面代码信息：
```javascript
{code_content}
```

# 任务
请基于以上页面需求和实际代码实现，为该页面编写一份详细的用户使用说明文档。
文档需要包含以下三个明确的部分：
1. 页面功能概述：简明扼要地描述页面的主要功能和用途。
2. 操作步骤：详细列出用户如何操作此页面的每一个步骤。请确保步骤清晰、完整，易于理解。将每个步骤作为一个独立的字符串在列表中提供。
3. 数据说明：解释页面中涉及的关键数据项、输入输出以及相关的数据逻辑或格式要求。

# 约束
- 输出内容中不要使用任何markdown格式的语法，如标题符号(#)、列表符号(-)、代码块(```)等
- 所有内容应该是纯文本格式
- 不要使用粗体、斜体、表格等任何markdown格式化元素

"""

    page_description = page_data.get("page_description", "")
    page_usage_instructions = page_data.get("page_usage_instructions", "")
    page_path = page_data.get("page_path", "")
    code_content = page_data.get("code_content", "")
    image_path = page_data.get("image_path")
    

    params = {
        "req_outline": req_outline,
        "page_name": page_name,
        "page_description": page_description,
        "page_usage_instructions": page_usage_instructions,
        "code_content": code_content,
    }
    
    result = await structured_output_handler(
        prompt_template=prompt_template_str,
        params=params,
        output_model=SinglePageInstructionResult
    )
    return {
        "page_name": page_name,
        "page_path": page_path,
        "image_path": image_path,
        "instructions": result
    }




async def generate_usage_doc(state: OverallState) -> Dict[str, Any]:
    """
    使用说明文档生成节点函数（对应文件: generate_usage_doc_node.py）
    
    Args:
        state: 整体状态对象
        
    Returns:
        Dict[str, Any]: 状态更新字典
    """
    olog.info(f"开始执行 {generate_usage_doc.__name__} 节点")

    # state变量
    # 输入变量
    req_outline = json.dumps(state.requirement_outline, ensure_ascii=False, indent=2)
    modules = state.requirement_outline.get('modules', [])
    
    # 从requirement_outline中提取所有页面信息
    pages = []
    for module in state.requirement_outline.get('modules', []):
        for page in module.get('pages', []):
            pages.append({
                'page_name': page.get('page_name'),
                'page_description': page.get('page_description'),
                'page_usage_instructions': page.get('page_usage_instructions'),
                'page_path': page.get('page_path')
            })
    codes = state.page_codes
    screenshots = state.screenshot_results
    project_name = state.requirement_outline["project_name"]
    docs_dir = state.docs_dir


    olog.info("开始生成系统文档所需的所有信息")
    
    # 1. 准备页面信息
    page_infos = pages.copy()
    
    for page_info in page_infos:
        page_name = page_info.get("page_name", "")
        page_path = page_info.get("page_path", "")
        page_info["image_path"] = screenshots.get(page_path) if screenshots else None
        page_info["code_content"] = codes.get(page_path, "") if codes else ""
    

    # 2. 并发执行所有生成任务
    olog.info(f"准备并发生成: 产品背景(1个), 模块描述({len(modules)}个), 页面说明({len(page_infos)}个)")
    
    # 创建所有并发任务
    tasks = []
    
    # 产品背景生成任务
    tasks.append(generate_product_background(req_outline))
    
    # 模块描述生成任务
    for module in modules:
        tasks.append(generate_single_module_description(module, req_outline))
    
    # 页面说明生成任务
    for page_info in page_infos:
        tasks.append(generate_single_page_instruction(req_outline, page_info))
    
    
    # 并发执行所有任务
    olog.info("开始并发执行所有生成任务...")
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 解析结果
    product_background = results[0] if not isinstance(results[0], Exception) else None
    if isinstance(results[0], Exception):
        olog.error(f"产品背景生成失败: {results[0]}")
    
    modules_description = []
    page_instructions = []
    
    # 处理模块描述结果
    for i, result in enumerate(results[1:1+len(modules)]):
        if isinstance(result, Exception):
            olog.error(f"模块 {modules[i].get('module_name', '')} 描述生成失败: {result}")
        else:
            modules_description.append(result)
    
    # 处理页面说明结果
    for i, result in enumerate(results[1+len(modules):]):
        if isinstance(result, Exception):
            olog.error(f"页面 {page_infos[i].get('page_name', '')} 说明生成失败: {result}")
        elif result:
            page_instructions.append(result)
    
    olog.info(f"并发执行完成: 产品背景({'成功' if product_background else '失败'}), 模块描述({len(modules_description)}/{len(modules)}个成功), 页面说明({len(page_instructions)}/{len(page_infos)}个成功)")

    # 3. 生成Word文档
    await AsyncPath(docs_dir).mkdir(parents=True, exist_ok=True)
    
    
    # 处理数据，转换为字典格式
    product_bg_dict = product_background.model_dump() if product_background else {}
    modules_dict = [module.model_dump() for module in modules_description]
    
    pages_dict = [{
        "page_name": page.get("page_name", ""),
        "page_path": page.get("page_path", ""),
        "image_path": page.get("image_path", ""),
        "instructions": page.get("instructions", {}).model_dump() if hasattr(page.get("instructions", {}), "model_dump") else page.get("instructions", {})
    } for page in page_instructions]

    # 使用默认软件名称
    software_name = project_name or "软件"

    # 创建Word文档和生成器
    document = Document()
    doc_generator = WordDocumentGenerator(document)

    # 3.1. 创建封面页
    doc_generator.create_cover_page(software_name, "使用说明书", "V1.0")
    
    # 3.2. 创建文档信息表格
    doc_generator.create_document_info_table()
    
    # 3.3. 创建修订历史表格
    revision_data = [
        {"version": "V0.1", "description": "创建基本用户界面"},
        {"version": "V0.2", "description": "添加核心功能模块"},
        {"version": "V1.0", "description": "完善功能和用户体验"}
    ]
    doc_generator.create_revision_history_table(revision_data)
    
    # 3.4. 添加分页符
    doc_generator.add_page_break()
    
    # 3.5. 设置页眉和页脚
    header_footer_generator = WordHeaderFooterGenerator()
    header_footer_generator.setup_header_footer(document, software_name)

    # 3.6. 添加文档主标题
    title = doc_generator.doc.add_heading("软件使用说明书", level=0)
    title.alignment = 1  # 居中对齐
    
    # 3.7. 添加产品背景
    doc_generator.add_heading("一、产品背景", level=1, size=doc_generator.section_font_size, bold=True)

    bg_sections = [("1.1 项目目的", "project_purpose"), ("1.2 目标用户", "target_users"), 
                   ("1.3 业务价值", "business_value"), ("1.4 主要功能特点", "key_features")]

    for title, key in bg_sections:
        doc_generator.add_heading(title, level=2, size=doc_generator.subsection_font_size, bold=True)
        doc_generator.add_paragraph(product_bg_dict.get(key, ""))

    # 3.8. 添加主要模块
    doc_generator.add_heading("二、主要模块", level=1, size=doc_generator.section_font_size, bold=True)

    for i, module in enumerate(modules_dict):
        module_name = module.get("module_name", "")
        doc_generator.add_heading(f'2.{i + 1} {module_name}', level=2, size=doc_generator.subsection_font_size, bold=True)

        # 添加模块各部分  
        for title, key in [("模块目的", "module_purpose"), ("主要功能点", "key_functions"), ("用户工作流程", "user_workflow")]:
            doc_generator.add_heading(title, level=3, size=doc_generator.normal_font_size, bold=True)
            doc_generator.add_paragraph(module.get(key, ""))

    # 3.9. 添加系统操作说明
    doc_generator.add_heading("三、系统操作说明", level=1, size=doc_generator.section_font_size, bold=True)

    for i, page_info in enumerate(pages_dict):
        page_name = page_info.get("page_name", "")
        page_path = page_info.get("page_path", "")
        image_path = page_info.get("image_path", "")
        instructions = page_info.get("instructions", {})

        # 添加页面标题
        doc_generator.add_heading(f"3.{i + 1} {page_name}", level=2, size=doc_generator.subsection_font_size, bold=True)
        doc_generator.add_heading("页面路径", level=3, size=doc_generator.normal_font_size, bold=True)
        doc_generator.add_paragraph(f"访问路径: {page_path}")

        # 添加页面截图
        if image_path and await AsyncPath(image_path).exists():
            doc_generator.add_heading("页面截图", level=3, size=doc_generator.normal_font_size, bold=True)
            doc_generator.add_paragraph("以下是页面界面的截图预览:")
            await doc_generator.add_image(image_path)

        # 添加页面说明各部分
        doc_generator.add_heading("页面功能概述", level=3, size=doc_generator.normal_font_size, bold=True)
        doc_generator.add_paragraph(instructions.get("functional_overview", ""))

        # 添加操作步骤
        doc_generator.add_heading("操作步骤", level=3, size=doc_generator.normal_font_size, bold=True)
        if steps := instructions.get("operational_steps", []):
            doc_generator.add_numbered_list(steps)

        # 添加数据说明
        doc_generator.add_heading("数据说明", level=3, size=doc_generator.normal_font_size, bold=True)
        doc_generator.add_paragraph(instructions.get("data_description", ""))

    # 保存文档
    doc_filename = f"{software_name}-软件使用说明书.docx"
    doc_path = smart_join_path(docs_dir, doc_filename)
    await asyncio.to_thread(document.save, doc_path)
    olog.info(f"Word文档创建完成，文档已保存到: {doc_path}")

    olog.info(f"{generate_usage_doc.__name__} 节点执行完成，生成文档路径: {doc_path}")
    return {"usage_doc_path": doc_path}

