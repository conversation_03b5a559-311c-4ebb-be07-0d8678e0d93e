import os
import random
import shutil
import time
import asyncio
from typing import Dict, Any

import aiofiles.os
from omni.log.log import olog
from agent.state import OverallState
from agent.utils.path_utils import smart_join_path
from common_config.common_config import LOCAL_TEMP_DIR

"""
沙箱环境准备智能体节点
"""


async def initialize_sandbox(state: OverallState) -> Dict[str, Any]:
    """
    初始化沙箱环境节点函数（对应文件: initialize_sandbox_node.py）
    
    Args:
        state: 整体状态对象
        
    Returns:
        Dict[str, Any]: 状态更新字典
    """
    olog.info(f"开始执行 {initialize_sandbox.__name__} 节点")
    
    timestamp = await asyncio.to_thread(lambda: int(time.time()))
    random_num = await asyncio.to_thread(random.randint, 100000, 999999)
    project_name = f"sca-sandbox-{timestamp}-{random_num}"

    sandbox_dir = smart_join_path(LOCAL_TEMP_DIR, project_name)
    project_dir = smart_join_path(sandbox_dir, "project")
    images_dir = smart_join_path(sandbox_dir, "images")
    docs_dir = smart_join_path(sandbox_dir, "docs")

    await aiofiles.os.makedirs(project_dir, exist_ok=True)
    await aiofiles.os.makedirs(images_dir, exist_ok=True)
    await aiofiles.os.makedirs(docs_dir, exist_ok=True)

    olog.debug(f"项目目录: {project_dir}")
    olog.debug(f"图片目录: {images_dir}")
    olog.debug(f"文档目录: {docs_dir}")

    olog.info(f"{initialize_sandbox.__name__} 节点执行完成")

    return {
        "project_dir": project_dir,
        "images_dir": images_dir,
        "docs_dir": docs_dir,
        "project_name": project_name,
        "sandbox_dir": sandbox_dir
    }