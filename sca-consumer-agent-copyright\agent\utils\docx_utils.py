import asyncio
from datetime import datetime, timedelta
from typing import Optional, List, Any

import aiofiles
import docx
from aiopath import AsyncPath
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_PARAGRAPH_ALIGNMENT
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from docx.shared import Pt, Inches, RGBColor

from omni.log.log import olog


# ==================== 服务类区域 ====================
# Word文档页眉页脚生成器和文档生成器

class WordHeaderFooterGenerator:
    """Word文档页眉页脚生成器 - 负责设置页眉页码格式"""

    def __init__(self):
        """初始化页眉页脚配置"""
        # 字体配置
        self.font_name = '宋体'
        self.font_size = Pt(12)

        # 页面距离配置
        self.header_distance = Inches(0.5)
        self.footer_distance = Inches(0.5)

        # 页码文本配置
        self.page_prefix = "第"
        self.page_suffix = "页"

        # 默认软件版本
        self.default_software_version = "v1.0"

    def setup_header_footer(self, document, software_name: str, software_version: str = None):
        """设置Word文档的页眉页脚
        
        Args:
            document: docx.Document 对象
            software_name: 软件名称
            software_version: 软件版本号，如果为None则使用默认版本
        """
        if software_version is None:
            software_version = self.default_software_version

        for section in document.sections:
            # 设置页眉
            header = section.header
            for child in list(header._element):
                header._element.remove(child)

            # 添加一个1x2的表格来实现左对齐和右对齐
            table = header.add_table(rows=1, cols=2,
                                     width=section.page_width - section.left_margin - section.right_margin)

            # 设置表格边框不可见
            tbl_pr = table._element.xpath('w:tblPr')[0]
            tbl_borders = OxmlElement('w:tblBorders')
            for border_name in ['top', 'left', 'bottom', 'right', 'insideH', 'insideV']:
                border_el = OxmlElement(f'w:{border_name}')
                border_el.set(qn('w:val'), 'none')
                tbl_borders.append(border_el)
            tbl_pr.append(tbl_borders)

            # 左侧单元格：软件名称和版本（左对齐）
            left_cell = table.cell(0, 0)
            left_para = left_cell.paragraphs[0]
            left_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
            name_run = left_para.add_run(f"{software_name} {software_version}")
            name_run.font.size = self.font_size
            name_run.font.name = self.font_name
            name_run._element.rPr.rFonts.set(qn('w:eastAsia'), self.font_name)

            # 右侧单元格：页码（右对齐）
            right_cell = table.cell(0, 1)
            right_para = right_cell.paragraphs[0]
            right_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT

            # 添加"第"字
            page_prefix_run = right_para.add_run(self.page_prefix)
            page_prefix_run.font.size = self.font_size
            page_prefix_run.font.name = self.font_name
            page_prefix_run._element.rPr.rFonts.set(qn('w:eastAsia'), self.font_name)

            # 添加页码字段
            fldChar1 = OxmlElement('w:fldChar')
            fldChar1.set(qn('w:fldCharType'), 'begin')

            instrText = OxmlElement('w:instrText')
            instrText.set(qn('xml:space'), 'preserve')
            instrText.text = "PAGE"

            fldChar2 = OxmlElement('w:fldChar')
            fldChar2.set(qn('w:fldCharType'), 'end')

            run = right_para.add_run()
            run._r.append(fldChar1)
            run._r.append(instrText)
            run._r.append(fldChar2)

            # 添加"页"字
            page_suffix_run = right_para.add_run(self.page_suffix)
            page_suffix_run.font.size = self.font_size
            page_suffix_run.font.name = self.font_name
            page_suffix_run._element.rPr.rFonts.set(qn('w:eastAsia'), self.font_name)

            # 设置页脚中的页码（右下角）
            footer = section.footer
            footer_para = footer.paragraphs[0] if footer.paragraphs else footer.add_paragraph()
            footer_para.clear()
            footer_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT

            # 添加"第"字
            footer_prefix_run = footer_para.add_run(self.page_prefix)
            footer_prefix_run.font.size = self.font_size
            footer_prefix_run.font.name = self.font_name
            footer_prefix_run._element.rPr.rFonts.set(qn('w:eastAsia'), self.font_name)

            # 添加页码字段
            fldChar1 = OxmlElement('w:fldChar')
            fldChar1.set(qn('w:fldCharType'), 'begin')

            instrText = OxmlElement('w:instrText')
            instrText.set(qn('xml:space'), 'preserve')
            instrText.text = "PAGE"

            fldChar2 = OxmlElement('w:fldChar')
            fldChar2.set(qn('w:fldCharType'), 'end')

            run = footer_para.add_run()
            run._r.append(fldChar1)
            run._r.append(instrText)
            run._r.append(fldChar2)

            # 添加"页"字
            footer_suffix_run = footer_para.add_run(self.page_suffix)
            footer_suffix_run.font.size = self.font_size
            footer_suffix_run.font.name = self.font_name
            footer_suffix_run._element.rPr.rFonts.set(qn('w:eastAsia'), self.font_name)

            # 设置页眉与正文的距离
            section.header_distance = self.header_distance
            # 设置页脚与正文的距离
            section.footer_distance = self.footer_distance
            section.different_first_page_header_footer = False


class WordDocumentGenerator:
    """Word文档生成器 - 负责创建和格式化Word文档"""

    def __init__(self, doc: docx.Document):
        self.doc = doc

        # 字体配置
        self.font_name = '宋体'
        self.normal_font_size = Pt(12)
        self.title_font_size = Pt(18)
        self.section_font_size = Pt(14)
        self.subsection_font_size = Pt(13)

        # 图片配置
        self.image_width = Inches(6.0)

        # 软件版本
        self.default_software_version = "v1.0"

        # UML模板路径
        self.class_uml_template_path = "agent/workflow1/uml_template/uml.puml"
        self.er_uml_template_path = "agent/workflow1/uml_template/er.puml"

        # 设置文档基本样式
        style = self.doc.styles['Normal']
        style.font.name = self.font_name
        style.font.size = self.normal_font_size

    def create_cover_page(self, software_name: str, document_type: str, software_version: str = "V1.0"):
        """创建封面页"""
        # 添加空行
        self.doc.add_paragraph().add_run().add_break()

        # 添加软件名称和版本号
        title_para = self.doc.add_paragraph()
        title_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        title_run = title_para.add_run(f"{software_name} {software_version}")
        title_run.font.size = Pt(26)
        title_run.font.bold = True
        title_run.font.name = self.font_name
        title_run._element.rPr.rFonts.set(qn("w:eastAsia"), self.font_name)

        self.doc.add_paragraph().add_run().add_break()

        # 添加文档类型标题
        subtitle_para = self.doc.add_paragraph()
        subtitle_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        subtitle_run = subtitle_para.add_run(document_type)
        subtitle_run.font.size = Pt(22)
        subtitle_run.font.bold = True
        subtitle_run.font.name = self.font_name
        subtitle_run._element.rPr.rFonts.set(qn("w:eastAsia"), self.font_name)

        self.doc.add_paragraph().add_run().add_break()

    def create_document_info_table(self):
        """创建文档信息表格"""
        # 添加文档信息表格
        table = self.doc.add_table(rows=5, cols=2)
        table.style = 'Table Grid'
        table.autofit = True

        # 获取今天的日期
        today = datetime.now()
        create_date = today - timedelta(days=10)  # 创建日期为10天前
        update_date = today  # 更新日期为今天

        cells = [
            ("文档版本:", "V1.0"),
            ("作者:", ""),
            ("负责人:", ""),
            ("创建日期:", create_date.strftime("%Y 年 %m 月 %d 日")),
            ("更新日期:", update_date.strftime("%Y 年 %m 月 %d 日"))
        ]

        for i, (label, value) in enumerate(cells):
            cell_label = table.cell(i, 0)
            cell_label.text = label
            cell_label.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT

            cell_value = table.cell(i, 1)
            cell_value.text = value
            cell_value.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.LEFT

        # 设置表格宽度
        table_width = Inches(5.5)  # 设置总宽度
        for row in table.rows:
            row.cells[0].width = table_width * 0.27  # 约27%
            row.cells[1].width = table_width * 0.73  # 约73%

        # 添加3个空行
        for _ in range(3):
            self.doc.add_paragraph().add_run().add_break()

    def create_revision_history_table(self, revision_data: List[dict]):
        """创建修订历史表格
        
        Args:
            revision_data: 修订历史数据列表，每个元素包含 version 和 description 字段
                          例如: [{"version": "V1.0", "description": "初始版本"}]
        """
        # 添加修订历史标题
        history_title = self.doc.add_paragraph()
        history_title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        history_run = history_title.add_run("修订历史")
        history_run.font.size = Pt(14)
        history_run.font.bold = True
        history_run.font.name = self.font_name
        history_run._element.rPr.rFonts.set(qn("w:eastAsia"), self.font_name)

        # 添加修订历史表格 (表头行 + 数据行数)
        history_table = self.doc.add_table(rows=len(revision_data) + 1, cols=4)
        history_table.style = 'Table Grid'

        # 设置表头
        headers = ["日期", "版本", "修改者", "描述"]
        for i, header in enumerate(headers):
            cell = history_table.cell(0, i)
            cell.text = header
            cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            run = cell.paragraphs[0].runs[0]
            run.font.bold = True

        # 设置表格内容 - 使用传入的参数数据
        today = datetime.now()
        for i, revision in enumerate(revision_data):
            # 根据索引计算日期 (每个版本间隔几天)
            revision_date = today - timedelta(days=(len(revision_data) - i - 1) * 5)
            date_str = revision_date.strftime("%Y-%m-%d")
            
            row = history_table.rows[i + 1]
            row_data = [date_str, revision["version"], "", revision["description"]]
            
            for j, text in enumerate(row_data):
                row.cells[j].text = text
                row.cells[j].paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

        # 设置修订历史表格宽度
        table_width = Inches(5.5)  # 设置总宽度与文档信息表格一致
        for row in history_table.rows:
            row.cells[0].width = table_width * 0.2  # 日期列占20%
            row.cells[1].width = table_width * 0.15  # 版本列占15%
            row.cells[2].width = table_width * 0.15  # 修改者列占15%
            row.cells[3].width = table_width * 0.5  # 描述列占50%

    def add_heading(self, text: str, level: int, size: Pt, bold: bool = False, color: Optional[RGBColor] = None):
        """为文档添加带样式的标题"""
        heading = self.doc.add_heading(text, level=level)
        if heading.runs:
            font = heading.runs[0].font
            font.name = self.font_name
            if size:
                font.size = size
            font.bold = bold
            if color:
                font.color.rgb = color

    def add_paragraph(self, text: str, alignment=WD_ALIGN_PARAGRAPH.JUSTIFY):
        """为文档添加带样式的段落"""
        if not text or not text.strip():
            return

        for para_text in text.split('\n'):
            if para_text.strip():
                paragraph = self.doc.add_paragraph(para_text)
                paragraph.alignment = alignment
                for run in paragraph.runs:
                    font = run.font
                    font.name = self.font_name

    async def add_image(self, image_path: str):
        """为文档添加图片，包含错误处理"""
        try:
            if await AsyncPath(image_path).exists():
                # 添加居中的图片
                paragraph = self.doc.add_paragraph()
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                paragraph.add_run().add_picture(image_path, width=self.image_width)
            else:
                # 添加图片占位符
                placeholder_text = f"[图片未找到: {image_path}]"
                paragraph = self.doc.add_paragraph(placeholder_text)
                paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
                olog.warning(f"图片文件未找到: {image_path}")
        except Exception as e:
            # 添加图片占位符
            placeholder_text = f"[图片加载失败: {image_path}]"
            paragraph = self.doc.add_paragraph(placeholder_text)
            paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
            olog.error(f"添加图片失败: {e}")

    def add_bullet_list(self, items: List[str]):
        """为文档添加项目符号列表"""
        for item in items:
            if item and str(item).strip():
                paragraph = self.doc.add_paragraph(str(item), style='List Bullet')
                paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
                for run in paragraph.runs:
                    font = run.font
                    font.name = self.font_name

    async def process_content(self, content_data: Any):
        """处理并添加章节内容 - 支持多种内容类型"""
        if isinstance(content_data, dict):
            # 处理字典类型的内容
            for subheading, subcontent in content_data.items():
                self.add_heading(subheading, level=2, size=self.subsection_font_size, bold=True)

                # 判断内容是否为图片路径
                if (isinstance(subcontent, str) and
                        subcontent.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))):
                    await self.add_image(subcontent)
                elif isinstance(subcontent, str):
                    self.add_paragraph(subcontent)
                elif isinstance(subcontent, list):
                    self.add_bullet_list(subcontent)
        elif isinstance(content_data, str):
            self.add_paragraph(content_data)
        elif isinstance(content_data, list):
            self.add_bullet_list(content_data)

    def add_page_break(self):
        """添加分页符"""
        self.doc.add_page_break()

    def add_numbered_list(self, items: List[str]):
        """添加编号列表"""
        for i, item in enumerate(items, 1):
            if item and str(item).strip():
                paragraph = self.doc.add_paragraph(f"{i}. {str(item).strip()}")
                paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
                for run in paragraph.runs:
                    font = run.font
                    font.name = self.font_name


class WordCodeDocumentGenerator:
    """Word代码文档生成器 - 专门负责生成符合软著要求的代码文档"""
    
    def __init__(self):
        """初始化代码文档生成器"""
        # 每页代码行数配置
        self.lines_per_page = 50
        
        # 字体配置
        self.font_name = '宋体'
        self.normal_font_size = Pt(10)
        self.small_font_size = Pt(8)
        self.tiny_font_size = Pt(6)
        
        # 页面边距配置
        self.page_margin = Inches(0.75)
        
        # 代码行长度阈值配置
        self.medium_line_threshold = 90
        self.long_line_threshold = 120
        
        # 页眉页脚生成器
        self.header_footer_generator = WordHeaderFooterGenerator()
    
    async def generate_code_document(self, code_lines: List[str], docx_path: str, 
                                   software_name: str, software_version: str = "v1.0") -> None:
        """
        生成代码文档Word文件
        
        Args:
            code_lines: 代码行列表
            docx_path: 输出的Word文档路径
            software_name: 软件名称
            software_version: 软件版本
        """
        # 初始化Word文档
        document = Document()
        
        # 设置文档边距
        self._setup_document_margins(document)
        
        # 设置文档样式
        self._setup_document_style(document)
        
        # 设置页眉页脚
        self.header_footer_generator.setup_header_footer(document, software_name, software_version)
        
        # 添加代码内容
        await self._add_code_content(document, code_lines)
        
        # 保存文档
        await asyncio.to_thread(document.save, docx_path)
        
        # 记录统计信息
        total_pages = (len(code_lines) + self.lines_per_page - 1) // self.lines_per_page
        olog.info(f"代码文档生成完成: {len(code_lines)} 行代码，共 {total_pages} 页")
    
    def _setup_document_margins(self, document: Document) -> None:
        """设置文档边距"""
        section = document.sections[0]
        section.top_margin = self.page_margin
        section.bottom_margin = self.page_margin
        section.left_margin = self.page_margin
        section.right_margin = self.page_margin
    
    def _setup_document_style(self, document: Document) -> None:
        """设置文档基本样式"""
        style = document.styles['Normal']
        font = style.font
        font.name = self.font_name
        font.size = self.normal_font_size
        
        # 设置段落格式以确保每页能容纳指定行数
        paragraph_format = style.paragraph_format
        paragraph_format.space_before = Pt(0)
        paragraph_format.space_after = Pt(0)
        paragraph_format.line_spacing = 1.0
    
    async def _add_code_content(self, document: Document, code_lines: List[str]) -> None:
        """向文档添加代码内容"""
        line_count_on_page = 0
        
        for i, line in enumerate(code_lines):
            # 每页行数控制
            if line_count_on_page >= self.lines_per_page:
                document.add_page_break()
                line_count_on_page = 0
            
            # 处理代码行
            safe_line = self._sanitize_code_line(line)
            line_with_number = f"{line_count_on_page + 1:02d}. {safe_line}"
            
            # 添加段落
            p = document.add_paragraph()
            self._clear_paragraph_format(p)
            
            # 添加代码行并设置字体
            run = p.add_run(line_with_number)
            run.font.name = self.font_name
            run.font.size = self._get_font_size_by_line_length(len(safe_line))
            
            line_count_on_page += 1
    
    def _sanitize_code_line(self, line: str) -> str:
        """清理代码行，处理编码问题和换行符"""
        # 处理可能的编码问题，并替换换行符
        return ''.join(c for c in line.replace('\n', ' ').replace('\r', ' ') if ord(c) < 128)
    
    def _clear_paragraph_format(self, paragraph) -> None:
        """清除段落格式，应用Normal样式"""
        paragraph.paragraph_format.space_before = None
        paragraph.paragraph_format.space_after = None
        paragraph.paragraph_format.line_spacing = None
    
    def _get_font_size_by_line_length(self, line_length: int) -> Pt:
        """根据代码行长度动态调整字体大小以防止自动换行"""
        if line_length > self.long_line_threshold:
            return self.tiny_font_size
        elif line_length > self.medium_line_threshold:
            return self.small_font_size
        else:
            return self.normal_font_size
    
    async def generate_from_files(self, file_paths: List[str], docx_path: str, 
                                software_name: str, software_version: str = "v1.0") -> None:
        """
        从多个文件生成代码文档
        
        Args:
            file_paths: 代码文件路径列表
            docx_path: 输出的Word文档路径
            software_name: 软件名称
            software_version: 软件版本
        """
        all_lines = []
        
        for file_path in file_paths:
            async_file_path = AsyncPath(file_path)
            
            if not await async_file_path.exists():
                olog.warning(f"文件不存在: {file_path}")
                continue
            
            try:
                # 读取并处理代码
                async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                    content = await f.read()
                    lines = content.split("\n")
                all_lines.extend(lines)
                olog.debug(f"已处理文件: {file_path}, 行数: {len(lines)}")
            except Exception as e:
                olog.error(f"读取文件失败 {file_path}: {e}")
                continue
        
        # 生成文档
        await self.generate_code_document(all_lines, docx_path, software_name, software_version)
