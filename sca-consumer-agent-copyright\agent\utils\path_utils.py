"""
智能路径拼接工具模块

提供智能、跨平台的路径拼接功能，支持混合传递各种格式的路径部分。
"""

from pathlib import Path


def smart_join_path(*paths: str) -> str:
    """
    智能拼接路径组件，自动处理各种混合输入格式
    
    功能特点：
    - 自动处理绝对路径（Windows盘符、Linux根路径）
    - 自动处理相对路径
    - 自动清理路径分隔符差异（/ 和 \\）
    - 支持混合传递各种格式的路径部分
    - 避免绝对路径覆盖前面路径组件的问题
    
    Args:
        *paths: 要拼接的路径组件字符串
        
    Returns:
        str: 拼接后的完整路径
        
    Examples:
        >>> smart_join_path("C:/temp/project", "app", "/public/devices", "page.tsx")
        "C:/temp/project/app/public/devices/page.tsx"
        
        >>> smart_join_path("/tmp/project", "\\app", "public\\devices", "page.tsx")
        "/tmp/project/app/public/devices/page.tsx"
        
        >>> smart_join_path(project_dir, "app", page_path, "page.tsx")
        # 自动处理各种 page_path 格式
    """
    if not paths:
        return ""
    
    # 过滤空路径并清理非首个路径的前导分隔符
    clean_paths = [
        path if i == 0 else path.lstrip('/\\')
        for i, path in enumerate(paths) if path
    ]
    
    return str(Path(*clean_paths)) if clean_paths else ""