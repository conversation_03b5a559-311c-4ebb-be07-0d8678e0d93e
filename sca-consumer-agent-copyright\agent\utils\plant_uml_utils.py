import aiofiles
import aiohttp
import asyncio
import os
from typing import Optional

from pydantic import BaseModel, Field
from omni.log.log import olog
from config.config import PLANTUML_CONFIG
from omni.exception.retry_decorator import retry_on_exception
from omni.llm.output_agent import structured_output_handler


class PlantUMLBadRequestError(Exception):
    """PlantUML 400错误异常"""
    pass


class UMLValidationResult(BaseModel):
    """UML验证结果模型"""
    is_uml_diagram: bool = Field(description="是否为UML图")
    confidence: float = Field(description="判断的置信度(0-1之间)", ge=0.0, le=1.0)
    reason: str = Field(description="判断理由")
    diagram_type: Optional[str] = Field(description="如果是UML图，识别出的图类型", default=None)


async def validate_uml_image(image_path: str) -> UMLValidationResult:
    """
    使用VL_LLM模型验证图片是否为UML图
    
    Args:
        image_path: 图片文件路径
        
    Returns:
        UMLValidationResult: 验证结果
        
    Raises:
        PlantUMLBadRequestError: 如果图片不是UML图
    """
    try:
        olog.info(f"开始验证UML图片: {image_path}")
        
        prompt_template = """
        请仔细分析这张图片，判断它是否为UML（统一建模语言）图。
        
        UML图的特征包括但不限于：
        1. 类图：包含类名、属性、方法，用矩形框表示
        2. 时序图：显示对象间的交互顺序，有生命线和消息箭头
        3. 用例图：包含用例、参与者、系统边界
        4. 活动图：显示活动流程，包含开始结束节点、活动节点、决策节点
        5. 状态图：显示状态转换，包含状态和转换箭头
        6. 组件图：显示组件及其依赖关系
        7. 部署图：显示硬件和软件组件的部署
        
        请根据以下标准进行判断：
        - 是否包含UML标准符号和元素
        - 是否遵循UML的图形表示规范
        - 是否具有UML图的结构特征
        
        如果图片中包含错误信息、空白内容、或者明显不是图表类内容，则不是UML图。
        
        请提供详细的判断理由和置信度。
        """
        
        params = {}
        
        result = await structured_output_handler(
            prompt_template=prompt_template,
            params=params,
            output_model=UMLValidationResult,
            llm_name="VL_LLM",
            tags=["uml_validation"],
            image_path=image_path
        )
        
        olog.info(f"UML验证结果: is_uml={result.is_uml_diagram}, confidence={result.confidence}, reason={result.reason}")
        
        # 如果不是UML图，抛出异常
        if not result.is_uml_diagram:
            error_msg = f"生成的图片不是有效的UML图。原因: {result.reason} (置信度: {result.confidence})"
            olog.error(error_msg)
            raise PlantUMLBadRequestError(error_msg)
        
        # 如果置信度太低，也抛出异常
        if result.confidence < 0.7:
            error_msg = f"UML图识别置信度过低: {result.confidence}。原因: {result.reason}"
            olog.error(error_msg)
            raise PlantUMLBadRequestError(error_msg)
        
        olog.info(f"UML图验证通过，图类型: {result.diagram_type}")
        return result
        
    except PlantUMLBadRequestError:
        # 直接重新抛出PlantUMLBadRequestError
        raise


async def generate_plant_uml(uml_text, output_path):
    """
    将PlantUML文本转换为PNG图片

    Args:
        uml_text: PlantUML文本内容
        output_path: 输出图片路径（PNG格式）

    Returns:
        dict: 包含生成的PNG文件路径，失败返回None
    """
    # 从配置文件获取服务器地址
    server_url = PLANTUML_CONFIG["server_url"]
    timeout = PLANTUML_CONFIG["timeout"]

    # 发送POST请求到PlantUML服务器获取PNG格式
    timeout_config = aiohttp.ClientTimeout(total=timeout)
    async with aiohttp.ClientSession(timeout=timeout_config) as session:
        async with session.post(
            server_url,
            data=uml_text.encode('utf-8'),
            headers={"Content-Type": "text/plain"}
        ) as response:
            # 只接受200状态码，其他状态码都报错
            if response.status != 200:
                error_text = await response.text()
                olog.error(f"PlantUML请求失败(状态码: {response.status}): {error_text}")
                raise Exception(f"HTTP {response.status}: {error_text}")

            # 读取响应内容
            response_content = await response.read()

            # 生成文件路径
            base_path = os.path.splitext(output_path)[0]
            png_path = base_path + '.png'

            # 保存PNG文件（二进制模式）
            async with aiofiles.open(png_path, "wb") as f:
                await f.write(response_content)

            olog.info(f"PlantUML图片已生成: PNG={png_path}")

            # 验证生成的图片是否为有效的UML图
            # await validate_uml_image(png_path)

            return png_path
    