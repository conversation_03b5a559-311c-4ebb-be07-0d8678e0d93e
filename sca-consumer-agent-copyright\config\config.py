from env import process_env

# Docker部署配置
if process_env == "dev":
    REMOTE_CONFIG = {
        "host": "**************",
        "port": 22,
        "username": "root",
        "password": "fuck2you@",
        "remote_dir": "/root/workspace/sca-web-sandbox",
        "remote_base_url": "http://**************:3031",
    }
if process_env == "prod":
    REMOTE_CONFIG = {
        "host": "*************",
        "port": 22,
        "username": "root",
        "password": "fuck2you@",
        "remote_dir": "/root/workspace/sca-web-sandbox",
        "remote_base_url": "http://*************:3031",
    }

# PlantUML 服务器配置
PLANTUML_CONFIG = {
    "server_url": "http://**************:8000/plantuml/png",
    "timeout": 30,
}
