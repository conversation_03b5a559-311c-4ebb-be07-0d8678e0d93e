#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --no-emit-index-url --no-emit-trusted-host requirements.in
#
aiofile==3.9.0
    # via aiopath
aiofiles==24.1.0
    # via -r requirements.in
aiopath==0.7.7
    # via -r requirements.in
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   aiopath
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
apscheduler==3.11.0
    # via -r requirements.in
asyncssh==2.21.0
    # via -r requirements.in
beanie==1.30.0
    # via -r requirements.in
caio==0.9.24
    # via aiofile
certifi==2025.7.14
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   beanie
    #   uvicorn
colorama==0.4.6
    # via
    #   click
    #   colorlog
    #   tqdm
    #   uvicorn
colorlog==6.9.0
    # via -r requirements.in
cos-python-sdk-v5==1.9.38
    # via -r requirements.in
crcmod==1.7
    # via cos-python-sdk-v5
cryptography==45.0.5
    # via
    #   asyncssh
    #   python-jose
distro==1.9.0
    # via openai
dnspython==2.7.0
    # via pymongo
ecdsa==0.19.1
    # via python-jose
fastapi==0.116.1
    # via
    #   -r requirements.in
    #   fastapi-sse
fastapi-sse==1.1.1
    # via -r requirements.in
greenlet==3.2.3
    # via
    #   playwright
    #   sqlalchemy
gunicorn==23.0.0
    # via
    #   -r requirements.in
    #   uvicorn-worker
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   langgraph-sdk
    #   langsmith
    #   openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jiter==0.10.0
    # via openai
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
langchain==0.3.27
    # via -r requirements.in
langchain-core==0.3.72
    # via
    #   langchain
    #   langchain-openai
    #   langchain-text-splitters
    #   langgraph
    #   langgraph-checkpoint
    #   langgraph-prebuilt
langchain-openai==0.3.28
    # via -r requirements.in
langchain-text-splitters==0.3.9
    # via langchain
langgraph==0.6.2
    # via -r requirements.in
langgraph-checkpoint==2.1.1
    # via
    #   langgraph
    #   langgraph-prebuilt
langgraph-prebuilt==0.6.2
    # via langgraph
langgraph-sdk==0.2.0
    # via langgraph
langsmith==0.4.8
    # via
    #   langchain
    #   langchain-core
lazy-model==0.2.0
    # via beanie
lxml==6.0.0
    # via python-docx
motor==3.7.1
    # via
    #   -r requirements.in
    #   beanie
openai==1.98.0
    # via langchain-openai
orjson==3.11.1
    # via
    #   langgraph-sdk
    #   langsmith
ormsgpack==1.10.0
    # via langgraph-checkpoint
packaging==25.0
    # via
    #   gunicorn
    #   langchain-core
    #   langsmith
pillow==11.3.0
    # via -r requirements.in
playwright==1.54.0
    # via -r requirements.in
pyasn1==0.6.1
    # via
    #   python-jose
    #   rsa
pycparser==2.22
    # via cffi
pycryptodome==3.23.0
    # via cos-python-sdk-v5
pydantic==2.11.7
    # via
    #   beanie
    #   fastapi
    #   langchain
    #   langchain-core
    #   langgraph
    #   langsmith
    #   lazy-model
    #   openai
pydantic-core==2.33.2
    # via pydantic
pyee==13.0.0
    # via playwright
pymongo==4.13.2
    # via motor
python-docx==1.2.0
    # via -r requirements.in
python-dotenv==1.1.1
    # via uvicorn
python-jose[cryptography]==3.5.0
    # via -r requirements.in
python-multipart==0.0.20
    # via -r requirements.in
pytz==2025.2
    # via -r requirements.in
pyyaml==6.0.2
    # via
    #   langchain
    #   langchain-core
    #   uvicorn
redis==6.2.0
    # via -r requirements.in
regex==2025.7.34
    # via tiktoken
requests==2.32.4
    # via
    #   cos-python-sdk-v5
    #   langchain
    #   langsmith
    #   requests-toolbelt
    #   tencentcloud-sdk-python-common
    #   tiktoken
requests-toolbelt==1.0.0
    # via langsmith
rsa==4.9.1
    # via python-jose
six==1.17.0
    # via
    #   cos-python-sdk-v5
    #   ecdsa
sniffio==1.3.1
    # via
    #   anyio
    #   openai
sqlalchemy==2.0.42
    # via langchain
starlette==0.47.2
    # via fastapi
tenacity==9.1.2
    # via langchain-core
tencentcloud-sdk-python-asr==3.0.1416
    # via -r requirements.in
tencentcloud-sdk-python-common==3.0.1416
    # via
    #   -r requirements.in
    #   tencentcloud-sdk-python-asr
    #   tencentcloud-sdk-python-tts
tencentcloud-sdk-python-tts==3.0.1416
    # via -r requirements.in
tiktoken==0.9.0
    # via langchain-openai
tqdm==4.67.1
    # via openai
typing-extensions==4.14.1
    # via
    #   anyio
    #   asyncssh
    #   beanie
    #   fastapi
    #   langchain-core
    #   openai
    #   pydantic
    #   pydantic-core
    #   pyee
    #   python-docx
    #   sqlalchemy
    #   starlette
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
tzdata==2025.2
    # via tzlocal
tzlocal==5.3.1
    # via apscheduler
urllib3==2.5.0
    # via requests
uvicorn[standard]==0.35.0
    # via
    #   -r requirements.in
    #   uvicorn-worker
uvicorn-worker==0.3.0
    # via -r requirements.in
watchfiles==1.1.0
    # via uvicorn
websockets==15.0.1
    # via uvicorn
xmltodict==0.14.2
    # via cos-python-sdk-v5
xxhash==3.5.0
    # via langgraph
zstandard==0.23.0
    # via langsmith
