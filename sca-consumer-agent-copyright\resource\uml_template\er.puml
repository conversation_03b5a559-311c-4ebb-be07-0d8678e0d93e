@startuml
title 教育系统ER图

' 实体定义
entity "学生" as Student {
  *student_id : 学生ID
  --
  name : 姓名
  gender : 性别
  birth_date : 出生日期
  address : 地址
  phone : 电话
  email : 电子邮件
}

entity "教师" as Teacher {
  *teacher_id : 教师ID
  --
  name : 姓名
  gender : 性别
  email : 电子邮件
  title : 职称
}

entity "课程" as Course {
  *course_id : 课程ID
  --
  name : 名称
  description : 描述
  credit : 学分
}

entity "班级" as Class {
  *class_id : 班级ID
  --
  name : 名称
  grade : 年级
  academic_year : 学年
}

entity "院系" as Department {
  *department_id : 院系ID
  --
  name : 名称
  description : 描述
}

entity "考试" as Exam {
  *exam_id : 考试ID
  --
  name : 名称
  exam_date : 考试日期
  total_score : 总分
}

entity "教学资源" as Resource {
  *resource_id : 资源ID
  --
  name : 名称
  type : 类型
  url : URL
}

entity "选课" as Enrollment {
  *enrollment_id : 选课ID
  --
  semester : 学期
  score : 成绩
}

entity "授课" as Teaching {
  *teaching_id : 授课ID
  --
  semester : 学期
}

entity "考试成绩" as ExamResult {
  *result_id : 成绩ID
  --
  score : 分数
}

' 关系定义
Student "N" -- "1" Class
Teacher "1" -- "N" Class
Teacher "N" -- "1" Department
Course "N" -- "1" Department
Student "N" -- "M" Course : > Enrollment
Teacher "N" -- "M" Course : > Teaching
Course "1" -- "N" Exam
Student "N" -- "M" Exam : > ExamResult
Teacher "1" -- "N" Resource
Course "1" -- "N" Resource
Class "N" -- "1" Department
Enrollment "N" -- "1" Student
Enrollment "N" -- "1" Course
Teaching "N" -- "1" Teacher
Teaching "N" -- "1" Course
ExamResult "N" -- "1" Student
ExamResult "N" -- "1" Exam
Resource "N" -- "1" Teacher
Resource "N" -- "1" Course
Exam "N" -- "1" Teacher
Class "1" -- "N" Exam

@enduml 