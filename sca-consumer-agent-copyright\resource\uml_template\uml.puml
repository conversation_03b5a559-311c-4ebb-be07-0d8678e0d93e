@startuml
title 教育系统数据模型

' 学生表
entity "Student" {
  *student_id : integer
  --
  name : string
  gender : string
  birth_date : date
  address : string
  phone : string
  email : string
}

' 教师表
entity "Teacher" {
  *teacher_id : integer
  --
  name : string
  gender : string
  email : string
  title : string
  department_id : integer
}

' 课程表
entity "Course" {
  *course_id : integer
  --
  name : string
  description : string
  credits : integer
  department_id : integer
}

' 班级表
entity "Class" {
  *class_id : integer
  --
  name : string
  grade : integer
  year : integer
  head_teacher_id : integer
}

' 学生选课表
entity "Enrollment" {
  *enrollment_id : integer
  --
  student_id : integer
  course_id : integer
  semester : string
  grade : float
}

' 教师授课表
entity "Teaching" {
  *teaching_id : integer
  --
  teacher_id : integer
  course_id : integer
  class_id : integer
  semester : string
}

' 院系表
entity "Department" {
  *department_id : integer
  --
  name : string
  description : string
  dean_id : integer
}

' 考试表
entity "Exam" {
  *exam_id : integer
  --
  course_id : integer
  name : string
  exam_date : date
  total_score : integer
}

' 学生考试成绩表
entity "ExamResult" {
  *result_id : integer
  --
  exam_id : integer
  student_id : integer
  score : float
}

' 教学资源表
entity "Resource" {
  *resource_id : integer
  --
  course_id : integer
  name : string
  type : string
  url : string
  teacher_id : integer
}

' 关系
Student |o--o{ Enrollment : "参与"
Student }o--|| Class : "属于"
Teacher |o--o{ Teaching : "教授"
Teacher }o--|| Department : "属于"
Course |o--o{ Enrollment : "被选修"
Course |o--o{ Teaching : "被教授"
Course |o--o{ Exam : "包含"
Department |o--o{ Course : "开设"
Exam |o--o{ ExamResult : "产生"
Student |o--o{ ExamResult : "获得"

@enduml