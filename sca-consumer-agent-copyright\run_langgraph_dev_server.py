from langgraph_api.cli import run_server


def start_langgraph_server():
    """在主线程中启动 LangGraph 开发服务器。"""
    
    # LangGraph 配置
    config = {
        "dependencies": [".", "../common-module"],
        "graphs": {
            "agent": "agent/graph.py:graph"
        }
    }
    
    graphs = config["graphs"]

    run_server(
        host="127.0.0.1",
        port=2024,
        reload=False,
        graphs=graphs,
        allow_blocking=True,
        open_browser=False
    )


if __name__ == "__main__":
    
    start_langgraph_server()

