# 服务部署

## 初始化服务器

```shell
dnf makecache
dnf install -y epel-release
dnf install -y htop
dnf install -y git
```

## 拉取项目代码

```shell
cd /root/projects
git clone -b master https://15928745302:<EMAIL>/w6513017/project-deploys.git
git clone -b dev https://15928745302:<EMAIL>/w6513017/software-copyright-agent.git
git clone -b master https://15928745302:<EMAIL>/w6513017/software-copyright-agent.git
```

## 数据库初始化

```javascript
use sca
db.sca.insert({name: "sca"})
db.createUser({
  user: 'sca',
  pwd: 'dCS9ghIOa1W4I0fX',
  roles:[{
    role: 'readWrite',
    db: 'sca'
  }]
})
```

## 设置环境

```text
api:把.env文件放根目录下
web:把env.js文件放根目录下
```

## 部署系统

略

## 设置负载均衡

配置 Caddyfile 并部署
