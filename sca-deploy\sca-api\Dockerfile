FROM mcr.microsoft.com/playwright/python:v1.54.0-noble

WORKDIR /work

# 设置时区
RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo "Asia/Shanghai" > /etc/timezone

# 配置国内 PyPI 源
RUN pip config set global.index-url https://mirrors.tencentyun.com/pypi/simple

# 拷贝依赖文件并安装
COPY ./build/requirements.txt ./
RUN pip install -r requirements.txt

# 拷贝源码
COPY ./build ./

ENTRYPOINT ["python", "start_all.py"]
