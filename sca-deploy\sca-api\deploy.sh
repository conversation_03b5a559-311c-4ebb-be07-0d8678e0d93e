export MODULE_GIT_PATH="/root/projects/software-copyright-agent"
export MODULE_PATH="${MODULE_GIT_PATH}/sca-api"
export DEPLOY_GIT_PATH="/root/projects/project-deploys"
export DEPLOY_PATH="${DEPLOY_GIT_PATH}/sca-deploy/sca-api"
export DOCKER_NAME="api-sca"
export DOCKER_PORT="5030"

cd ${MODULE_GIT_PATH} && git pull
cd ${DEPLOY_GIT_PATH} && git pull
rm -rf "${DEPLOY_PATH}/build" && mkdir -p "${DEPLOY_PATH}/build"
cp -r "${MODULE_PATH}/." "${DEPLOY_PATH}/build"
cd "${DEPLOY_PATH}"
docker build -t "${DOCKER_NAME}" .
bash ${DEPLOY_PATH}/generate-compose.sh ${DOCKER_NAME} ${DOCKER_PORT}
docker compose down
docker compose up -d
echo "Deployed ${DOCKER_NAME} on port ${DOCKER_PORT}"