#!/bin/bash

# 配置
PROJECT_NAME="sca-web-sandbox"
DEPLOY_MODULE_NAME="sca-deploy"
DOCKER_PORT="3031"

# 路径
PROJECT_BASE="/root/projects/software-copyright-agent"
DEPLOY_BASE="/root/projects/project-deploys"
BUILD_PATH="/tmp/build/${PROJECT_NAME}"

# 更新代码
cd ${PROJECT_BASE} && git pull
cd ${DEPLOY_BASE} && git pull

# 构建
rm -rf ${BUILD_PATH} && mkdir -p ${BUILD_PATH}
cp -r ${PROJECT_BASE}/${PROJECT_NAME}/. ${BUILD_PATH}/
cp ${DEPLOY_BASE}/${DEPLOY_MODULE_NAME}/${PROJECT_NAME}/Dockerfile ${BUILD_PATH}/
cp ${DEPLOY_BASE}/${DEPLOY_MODULE_NAME}/${PROJECT_NAME}/generate-compose.sh ${BUILD_PATH}/

# 部署
cd ${BUILD_PATH}
docker build -t ${PROJECT_NAME} .
bash generate-compose.sh ${PROJECT_NAME} ${DOCKER_PORT}
docker compose down --remove-orphans && docker compose up -d
echo "Deployed ${PROJECT_NAME} on port ${DOCKER_PORT}"