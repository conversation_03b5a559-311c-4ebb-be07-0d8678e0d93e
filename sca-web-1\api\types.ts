// API请求参数接口
export interface ApiParams {
  resource: string;           // 资源标识
  method_name: string;        // 方法名称  
  data?: Record<string, any>; // 可选的处理数据，默认为空对象
}

// API统一响应格式接口
export interface ApiResponse<T = any> {
  code: number;        // 业务状态码 (401/403/500等)
  message: string;     // 响应消息
  data: T;            // 实际返回数据
}

// 业务状态码枚举
export enum BusinessCode {
  SUCCESS = 200,       // 成功
  UNAUTHORIZED = 401,  // 未授权
  FORBIDDEN = 403,     // 禁止访问
  SERVER_ERROR = 500   // 服务器错误
}