// 仪表板相关类型定义

// 仪表板统计数据
export interface DashboardStatsRequest {
  action: 'getDashboardStats';
  dateRange?: {
    startDate: string;
    endDate: string;
  };
}

export interface DashboardStatsResponse {
  totalUsers: number;
  activeUsers: number;
  totalProjects: number;
  completedProjects: number;
  pendingTasks: number;
  totalRevenue?: number;
  statistics: {
    userGrowthRate: number;      // 用户增长率
    projectCompletionRate: number; // 项目完成率
    activeUserRate: number;       // 活跃用户率
    systemUsageRate: number;      // 系统使用率
  };
  chartData: {
    userGrowth: ChartDataPoint[];
    projectProgress: ChartDataPoint[];
    revenueData?: ChartDataPoint[];
  };
}

// 图表数据点
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

// 最近活动
export interface RecentActivitiesRequest {
  action: 'getRecentActivities';
  limit?: number;
  type?: ActivityType[];
}

export type ActivityType = 
  | 'user_login' 
  | 'user_logout' 
  | 'user_created' 
  | 'user_updated'
  | 'project_created' 
  | 'project_updated'
  | 'project_completed'
  | 'project_deleted'
  | 'system_maintenance'
  | 'security_alert';

export interface Activity {
  id: string;
  type: ActivityType;
  title: string;
  message: string;
  timestamp: string;
  userId?: string;
  userName?: string;
  userAvatar?: string;
  metadata?: Record<string, any>;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}

export interface RecentActivitiesResponse {
  activities: Activity[];
  total: number;
}

// 系统状态
export interface SystemStatusRequest {
  action: 'getSystemStatus';
}

export interface SystemStatusResponse {
  status: 'healthy' | 'warning' | 'error';
  uptime: number;           // 运行时间(秒)
  version: string;          // 系统版本
  lastUpdate: string;       // 最后更新时间
  services: ServiceStatus[];
  resources: ResourceUsage;
}

export interface ServiceStatus {
  name: string;
  status: 'running' | 'stopped' | 'error';
  uptime: number;
  version: string;
  lastCheck: string;
}

export interface ResourceUsage {
  cpu: {
    usage: number;        // CPU使用率 (0-100)
    cores: number;        // CPU核心数
  };
  memory: {
    used: number;         // 已使用内存 (MB)
    total: number;        // 总内存 (MB)
    usage: number;        // 内存使用率 (0-100)
  };
  disk: {
    used: number;         // 已使用磁盘空间 (GB)
    total: number;        // 总磁盘空间 (GB)
    usage: number;        // 磁盘使用率 (0-100)
  };
  network: {
    inbound: number;      // 入站流量 (MB/s)
    outbound: number;     // 出站流量 (MB/s)
  };
}

// 通知相关
export interface NotificationsRequest {
  action: 'getNotifications';
  page?: number;
  size?: number;
  unreadOnly?: boolean;
}

export interface Notification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
  actionLabel?: string;
}

export interface NotificationsResponse {
  notifications: Notification[];
  total: number;
  unreadCount: number;
}

// 标记通知为已读
export interface MarkNotificationReadRequest {
  action: 'markNotificationRead';
  notificationId: string;
}

// 批量标记通知为已读
export interface MarkAllNotificationsReadRequest {
  action: 'markAllNotificationsRead';
}