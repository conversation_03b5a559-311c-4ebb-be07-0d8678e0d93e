// 用户管理相关类型定义

// 重新导出认证类型中的UserInfo
export type { UserInfo } from './auth';

// 用户列表相关
export interface UserListRequest {
  action: 'getUserList';
  page?: number;
  size?: number;
  keyword?: string;
  role?: string;
  status?: 'active' | 'inactive' | 'all';
}

export interface UserListResponse {
  users: UserInfo[];
  total: number;
  page: number;
  size: number;
}

// 创建用户
export interface CreateUserRequest {
  action: 'createUser';
  username: string;
  email: string;
  password: string;
  role: 'admin' | 'user' | 'guest';
  avatar?: string;
}

export interface CreateUserResponse {
  userId: string;
  message: string;
}

// 更新用户
export interface UpdateUserRequest {
  action: 'updateUser';
  userId: string;
  username?: string;
  email?: string;
  role?: 'admin' | 'user' | 'guest';
  avatar?: string;
  status?: 'active' | 'inactive';
}

// 删除用户
export interface DeleteUserRequest {
  action: 'deleteUser';
  userId: string;
}

// 批量操作
export interface BatchUserOperationRequest {
  action: 'batchUserOperation';
  operation: 'delete' | 'activate' | 'deactivate';
  userIds: string[];
}

// 用户详情
export interface GetUserDetailRequest {
  action: 'getUserDetail';
  userId: string;
}

export interface UserDetailResponse extends UserInfo {
  lastLoginAt?: string;
  status: 'active' | 'inactive';
  permissions: string[];
}

// 用户统计
export interface UserStatsRequest {
  action: 'getUserStats';
}

export interface UserStatsResponse {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  adminUsers: number;
  regularUsers: number;
  guestUsers: number;
  newUsersThisMonth: number;
}