@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
    --color-sidebar-ring: var(--sidebar-ring);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar: var(--sidebar);
    --color-chart-5: var(--chart-5);
    --color-chart-4: var(--chart-4);
    --color-chart-3: var(--chart-3);
    --color-chart-2: var(--chart-2);
    --color-chart-1: var(--chart-1);
    --color-ring: var(--ring);
    --color-input: var(--input);
    --color-border: var(--border);
    --color-destructive: var(--destructive);
    --color-accent-foreground: var(--accent-foreground);
    --color-accent: var(--accent);
    --color-muted-foreground: var(--muted-foreground);
    --color-muted: var(--muted);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-secondary: var(--secondary);
    --color-primary-foreground: var(--primary-foreground);
    --color-primary: var(--primary);
    --color-popover-foreground: var(--popover-foreground);
    --color-popover: var(--popover);
    --color-card-foreground: var(--card-foreground);
    --color-card: var(--card);
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
}

:root {
    --radius: 0.75rem;
    /* Google科技配色浅色主题 - 基于Material Design色彩体系 */
    --background: oklch(1 0 0);
    --foreground: oklch(0.13 0.004 240);
    --card: oklch(0.99 0.001 240);
    --card-foreground: oklch(0.13 0.004 240);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.13 0.004 240);
    --primary: oklch(0.54 0.196 222); /* Google Blue #4285F4 */
    --primary-foreground: oklch(1 0 0);
    --secondary: oklch(0.95 0.003 240);
    --secondary-foreground: oklch(0.25 0.008 240);
    --muted: oklch(0.97 0.002 240);
    --muted-foreground: oklch(0.47 0.006 240);
    --accent: oklch(0.65 0.165 138); /* Google Green #34A853 */
    --accent-foreground: oklch(1 0 0);
    --destructive: oklch(0.62 0.204 27); /* Google Red #EA4335 */
    --border: oklch(0.90 0.005 240);
    --input: oklch(0.98 0.001 240);
    --ring: oklch(0.54 0.196 222);
    --chart-1: oklch(0.54 0.196 222); /* Google Blue */
    --chart-2: oklch(0.65 0.165 138); /* Google Green */
    --chart-3: oklch(0.74 0.141 87); /* Google Yellow #FBBC04 */
    --chart-4: oklch(0.62 0.204 27); /* Google Red */
    --chart-5: oklch(0.45 0.102 293); /* Google Purple */
    --sidebar: oklch(0.98 0.001 240);
    --sidebar-foreground: oklch(0.20 0.006 240);
    --sidebar-primary: oklch(0.54 0.196 222);
    --sidebar-primary-foreground: oklch(1 0 0);
    --sidebar-accent: oklch(0.95 0.003 240);
    --sidebar-accent-foreground: oklch(0.25 0.008 240);
    --sidebar-border: oklch(0.88 0.008 240);
    --sidebar-ring: oklch(0.54 0.196 222);
}

.dark {
    /* Google科技配色深色主题 - 基于Material Design Dark色彩体系 */
    --background: oklch(0.09 0.003 240);
    --foreground: oklch(0.93 0.002 240);
    --card: oklch(0.12 0.004 240);
    --card-foreground: oklch(0.93 0.002 240);
    --popover: oklch(0.10 0.003 240);
    --popover-foreground: oklch(0.93 0.002 240);
    --primary: oklch(0.61 0.188 222); /* Google Blue 亮色版 */
    --primary-foreground: oklch(0.09 0.003 240);
    --secondary: oklch(0.18 0.006 240);
    --secondary-foreground: oklch(0.85 0.004 240);
    --muted: oklch(0.15 0.004 240);
    --muted-foreground: oklch(0.68 0.006 240);
    --accent: oklch(0.70 0.155 138); /* Google Green 亮色版 */
    --accent-foreground: oklch(0.09 0.003 240);
    --destructive: oklch(0.68 0.196 27); /* Google Red 亮色版 */
    --border: oklch(0.22 0.008 240);
    --input: oklch(0.12 0.004 240);
    --ring: oklch(0.61 0.188 222);
    --chart-1: oklch(0.61 0.188 222); /* Google Blue */
    --chart-2: oklch(0.70 0.155 138); /* Google Green */
    --chart-3: oklch(0.78 0.130 87); /* Google Yellow */
    --chart-4: oklch(0.68 0.196 27); /* Google Red */
    --chart-5: oklch(0.58 0.095 293); /* Google Purple */
    --sidebar: oklch(0.10 0.003 240);
    --sidebar-foreground: oklch(0.88 0.003 240);
    --sidebar-primary: oklch(0.61 0.188 222);
    --sidebar-primary-foreground: oklch(0.09 0.003 240);
    --sidebar-accent: oklch(0.18 0.006 240);
    --sidebar-accent-foreground: oklch(0.85 0.004 240);
    --sidebar-border: oklch(0.20 0.010 240);
    --sidebar-ring: oklch(0.61 0.188 222);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground;
    }
}
