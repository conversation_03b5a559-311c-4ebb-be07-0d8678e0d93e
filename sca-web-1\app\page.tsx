'use client'

import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { GlobalSkeleton } from "@/components/loading/global-skeleton";

export default function Home() {
    const { isAuthenticated, loading } = useAuth();
    const router = useRouter();

    useEffect(() => {
        if (!loading) {
            if (isAuthenticated) {
                router.push('/protected/dashboard');
            } else {
                router.push('/public/login');
            }
        }
    }, [isAuthenticated, loading, router]);

    if (loading) {
        return <GlobalSkeleton />;
    }

    return null;
}
