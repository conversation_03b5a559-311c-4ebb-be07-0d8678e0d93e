'use client'

import { But<PERSON> } from "@/components/ui/button"
import { useAuth } from "@/contexts/auth-context"

export default function DashboardPage() {
    const { logout } = useAuth()

    const handleLogout = () => {
        logout()
        window.location.href = '/public/login'
    }

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold">仪表板</h1>
                <Button variant="outline" onClick={handleLogout}>
                    退出登录
                </Button>
            </div>
        </div>
    )
}
