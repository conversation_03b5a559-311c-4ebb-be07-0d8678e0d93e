'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { GlobalSkeleton } from '@/components/loading/global-skeleton'

interface AuthContextType {
  isAuthenticated: boolean
  token: string | null
  login: (token: string) => void
  logout: () => void
  loading: boolean
  isPublicRoute: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthWrapper')
  }
  return context
}

interface AuthWrapperProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export const AuthWrapper: React.FC<AuthWrapperProps> = ({ 
  children, 
  fallback = <GlobalSkeleton /> 
}) => {
  const [token, setToken] = useState<string | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true)
  const pathname = usePathname()
  const router = useRouter()

  // 定义公共路由
  const publicRoutes = ['/public']
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route))

  useEffect(() => {
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      setToken(savedToken)
      setIsAuthenticated(true)
    }
    setLoading(false)
  }, [])

  useEffect(() => {
    if (!loading && !isAuthenticated && !isPublicRoute) {
      router.push('/public/login')
    }
  }, [isAuthenticated, loading, isPublicRoute, router])

  const login = (newToken: string) => {
    localStorage.setItem('token', newToken)
    setToken(newToken)
    setIsAuthenticated(true)
  }

  const logout = () => {
    localStorage.removeItem('token')
    setToken(null)
    setIsAuthenticated(false)
  }

  const value = {
    isAuthenticated,
    token,
    login,
    logout,
    loading,
    isPublicRoute
  }

  // 加载状态处理
  if (loading && !isPublicRoute) {
    return <>{fallback}</>
  }

  // 未认证且非公共路由时不渲染内容
  if (!isAuthenticated && !isPublicRoute) {
    return null
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}