"use client";
import { Home, LogOut, Plus, Search } from 'lucide-react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useRouter } from 'next/navigation';

export default function HomePage() {
  const router = useRouter();
  
  // Mock data for featured raffles
  const featuredRaffles = [
    {
      id: '1',
      title: 'Summer Giveaway',
      description: 'Win amazing prizes in our summer special event!',
      participants: 1243,
      endDate: '2023-08-31T23:59:59',
      prizeCount: 5,
      isNew: true,
    },
    {
      id: '2',
      title: 'Tech Gadgets Raffle',
      description: 'Latest tech gadgets up for grabs!',
      participants: 892,
      endDate: '2023-08-25T23:59:59',
      prizeCount: 3,
      isNew: false,
    },
    {
      id: '3',
      title: 'Charity Fundraiser',
      description: 'Participate and support a good cause!',
      participants: 567,
      endDate: '2023-09-15T23:59:59',
      prizeCount: 10,
      isNew: true,
    },
  ];

  // Mock user data
  const user = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: '',
    isAdmin: true,
  };

  const handleLogout = () => {
    // Logout logic here
    router.push('/public/login');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Bar */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            <Link href="/public/home" className="flex items-center space-x-2">
              <Home className="h-6 w-6 text-primary" />
              <span className="font-bold text-primary">幸运抽奖通</span>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
              <input
                type="text"
                placeholder="Search raffles..."
                className="h-10 w-full rounded-lg border pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Avatar>
                <AvatarImage src={user.avatar} />
                <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <span className="text-sm font-medium">{user.name}</span>
                <span className="text-xs text-muted-foreground">{user.email}</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleLogout}
                className="text-muted-foreground hover:text-destructive"
              >
                <LogOut className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container py-8 px-4">
        {/* Hero Section */}
        <section className="mb-12 rounded-xl bg-gradient-to-r from-primary to-secondary p-8 text-white">
          <h1 className="mb-4 text-4xl font-bold">Welcome to 幸运抽奖通</h1>
          <p className="mb-6 max-w-2xl text-lg">
            Discover exciting raffles and win amazing prizes. Participate in our
            featured events or create your own!
          </p>
          <div className="flex space-x-4">
            <Button variant="secondary" asChild>
              <Link href="/public/raffles">Browse All Raffles</Link>
            </Button>
            {user.isAdmin && (
              <Button variant="outline" className="text-white" asChild>
                <Link href="/public/raffles/create">
                  <Plus className="mr-2 h-4 w-4" /> Create Raffle
                </Link>
              </Button>
            )}
          </div>
        </section>

        {/* Featured Raffles */}
        <section className="mb-12">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-2xl font-bold">Featured Raffles</h2>
            <Button variant="ghost" asChild>
              <Link href="/public/raffles" className="text-primary">
                View All
              </Link>
            </Button>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {featuredRaffles.map((raffle) => (
              <Card key={raffle.id} className="hover:shadow-lg">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>{raffle.title}</CardTitle>
                    {raffle.isNew && (
                      <Badge variant="secondary" className="ml-2">
                        New
                      </Badge>
                    )}
                  </div>
                  <CardDescription>{raffle.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      {raffle.participants.toLocaleString()} participants
                    </span>
                    <span className="font-medium">
                      Ends: {formatDate(raffle.endDate)}
                    </span>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Badge variant="outline">
                    {raffle.prizeCount} prize{raffle.prizeCount > 1 ? 's' : ''}
                  </Badge>
                  <Button asChild>
                    <Link href={`/public/raffles/${raffle.id}`}>Participate</Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </section>

        {/* Stats Section */}
        <section className="mb-12 rounded-lg border bg-white p-6 shadow-sm">
          <h2 className="mb-6 text-2xl font-bold">System Statistics</h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div className="rounded-lg border p-6">
              <h3 className="mb-2 text-lg font-medium">Active Raffles</h3>
              <p className="text-3xl font-bold text-primary">24</p>
            </div>
            <div className="rounded-lg border p-6">
              <h3 className="mb-2 text-lg font-medium">Total Participants</h3>
              <p className="text-3xl font-bold text-secondary">5,678</p>
            </div>
            <div className="rounded-lg border p-6">
              <h3 className="mb-2 text-lg font-medium">Prizes Awarded</h3>
              <p className="text-3xl font-bold text-accent-foreground">342</p>
            </div>
          </div>
        </section>

        {/* How It Works */}
        <section className="mb-12">
          <h2 className="mb-6 text-2xl font-bold">How It Works</h2>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="rounded-lg border p-6">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white">
                <span className="text-xl font-bold">1</span>
              </div>
              <h3 className="mb-2 text-lg font-medium">Browse Raffles</h3>
              <p className="text-muted-foreground">
                Explore our collection of exciting raffles and choose the ones you
                want to participate in.
              </p>
            </div>
            <div className="rounded-lg border p-6">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-secondary text-white">
                <span className="text-xl font-bold">2</span>
              </div>
              <h3 className="mb-2 text-lg font-medium">Participate</h3>
              <p className="text-muted-foreground">
                Click the participate button and follow the simple steps to enter
                the raffle.
              </p>
            </div>
            <div className="rounded-lg border p-6">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-accent text-accent-foreground">
                <span className="text-xl font-bold">3</span>
              </div>
              <h3 className="mb-2 text-lg font-medium">Win Prizes</h3>
              <p className="text-muted-foreground">
                Check the results after the raffle ends to see if you've won any
                amazing prizes!
              </p>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t bg-white py-8">
        <div className="container px-4">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
            <div>
              <h3 className="mb-4 text-lg font-semibold">幸运抽奖通</h3>
              <p className="text-muted-foreground">
                Your one-stop solution for all raffle activities and prize
                giveaways.
              </p>
            </div>
            <div>
              <h3 className="mb-4 text-lg font-semibold">Quick Links</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/public/home"
                    className="text-muted-foreground hover:text-primary"
                  >
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    href="/public/raffles"
                    className="text-muted-foreground hover:text-primary"
                  >
                    All Raffles
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-primary"
                  >
                    About Us
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-primary"
                  >
                    Contact
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="mb-4 text-lg font-semibold">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-primary"
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-primary"
                  >
                    Privacy Policy
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="mb-4 text-lg font-semibold">Connect With Us</h3>
              <div className="flex space-x-4">
                <Button variant="outline" size="icon">
                  <span className="sr-only">Twitter</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-5 w-5"
                  >
                    <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                  </svg>
                </Button>
                <Button variant="outline" size="icon">
                  <span className="sr-only">Facebook</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-5 w-5"
                  >
                    <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                  </svg>
                </Button>
                <Button variant="outline" size="icon">
                  <span className="sr-only">Instagram</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-5 w-5"
                  >
                    <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                    <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                  </svg>
                </Button>
              </div>
            </div>
          </div>
          <Separator className="my-6" />
          <p className="text-center text-sm text-muted-foreground">
            © 2023 幸运抽奖通. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
