"use client";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useFieldArray } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { CalendarIcon, Loader2, X } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

const formSchema = z.object({
  title: z.string().min(2, {
    message: "Title must be at least 2 characters.",
  }),
  description: z.string().min(10, {
    message: "Description must be at least 10 characters.",
  }),
  startDate: z.date(),
  endDate: z.date(),
  prizes: z.array(
    z.object({
      name: z.string().min(1, { message: "Prize name is required" }),
      quantity: z.number().min(1, { message: "Quantity must be at least 1" }),
    })
  ),
});

type RaffleFormValues = z.infer<typeof formSchema>;

interface Prize {
  name: string;
  quantity: number;
}

interface RaffleData {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  prizes: Prize[];
}

export default function EditRafflePage({ params }: { params: { raffleId: string } }) {
  const router = useRouter();
  
  // Mock data - in a real app this would come from an API call
  const mockRaffleData: RaffleData = {
    id: params.raffleId,
    title: "Summer Giveaway",
    description: "Win amazing prizes this summer season!",
    startDate: new Date("2023-06-15"),
    endDate: new Date("2023-08-31"),
    prizes: [
      { name: "iPhone 14 Pro", quantity: 1 },
      { name: "AirPods Pro", quantity: 3 },
      { name: "Amazon Gift Card $100", quantity: 5 },
    ],
  };

  const form = useForm<RaffleFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: mockRaffleData.title,
      description: mockRaffleData.description,
      startDate: mockRaffleData.startDate,
      endDate: mockRaffleData.endDate,
      prizes: mockRaffleData.prizes,
    },
  });

  const { fields, append, remove } = useFieldArray({
    name: "prizes",
    control: form.control,
  });

  async function onSubmit(values: RaffleFormValues) {
    try {
      // In a real app, this would be an API call to update the raffle
      console.log("Updating raffle:", values);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success("Raffle updated successfully!");
      router.push(`/public/raffles/${params.raffleId}`);
    } catch (error) {
      toast.error("Failed to update raffle. Please try again.");
      console.error(error);
    }
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Edit Raffle</h1>
        <Button variant="outline" onClick={() => router.push(`/public/raffles/${params.raffleId}`)}>
          <X className="mr-2 h-4 w-4" /> Cancel
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Raffle Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter raffle title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe your raffle event"
                          className="min-h-[120px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-[240px] pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date() || date > form.getValues("endDate")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-[240px] pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < form.getValues("startDate") || date < new Date()
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Prizes</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => append({ name: "", quantity: 1 })}
                  >
                    Add Prize
                  </Button>
                </div>
                <Separator className="my-4" />

                <div className="space-y-4">
                  {fields.map((field, index) => (
                    <div key={field.id} className="flex items-start gap-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-1">
                        <FormField
                          control={form.control}
                          name={`prizes.${index}.name`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={cn(index !== 0 && "sr-only")}>
                                Prize Name
                              </FormLabel>
                              <FormControl>
                                <Input placeholder="Enter prize name" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`prizes.${index}.quantity`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={cn(index !== 0 && "sr-only")}>
                                Quantity
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  placeholder="1"
                                  {...field}
                                  onChange={(event) =>
                                    field.onChange(Number(event.target.value))
                                  }
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="mt-8"
                        onClick={() => remove(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}

                  {fields.length === 0 && (
                    <div className="flex items-center justify-center h-24 rounded-md border border-dashed">
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">
                          No prizes added yet
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push(`/public/raffles/${params.raffleId}`)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
