"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Gift, Clock, Users, Edit, List } from "lucide-react";

type Prize = {
  id: string;
  name: string;
  description: string;
  quantity: number;
  imageUrl?: string;
};

type Raffle = {
  id: string;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  participationCount: number;
  prizes: Prize[];
  rules: string[];
  isParticipated: boolean;
  isAdmin: boolean;
};

export default function RaffleDetailPage() {
  const params = useParams();
  const [raffle, setRaffle] = useState<Raffle | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    completed: false
  });

  useEffect(() => {
    // Mock data fetch
    const fetchRaffleData = async () => {
      try {
        // In a real app, this would be an API call
        const mockRaffle: Raffle = {
          id: params.raffleId as string,
          title: "Annual Lucky Draw",
          description: "Participate in our annual lucky draw event to win amazing prizes! Open to all registered users with one entry per person.",
          startTime: "2024-03-01T00:00:00Z",
          endTime: "2024-04-30T23:59:59Z",
          participationCount: 1243,
          prizes: [
            {
              id: "1",
              name: "First Prize",
              description: "MacBook Pro 16-inch",
              quantity: 1,
            },
            {
              id: "2",
              name: "Second Prize",
              description: "iPhone 15 Pro",
              quantity: 3,
            },
            {
              id: "3",
              name: "Third Prize",
              description: "AirPods Pro",
              quantity: 10,
            },
          ],
          rules: [
            "Each user can only participate once",
            "Winners will be announced after the event ends",
            "Prizes will be distributed within 30 working days after the event",
          ],
          isParticipated: false,
          isAdmin: false,
        };

        setRaffle(mockRaffle);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching raffle data:", error);
        setLoading(false);
      }
    };

    fetchRaffleData();
  }, [params.raffleId]);

  useEffect(() => {
    if (!raffle) return;
    
    const timer = setInterval(() => {
      const now = new Date();
      const endDate = new Date(raffle.endTime);
      const diff = endDate.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeLeft({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          completed: true
        });
        clearInterval(timer);
        return;
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setTimeLeft({
        days,
        hours,
        minutes,
        seconds,
        completed: false
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [raffle]);

  const handleParticipate = () => {
    // In a real app, this would call an API to participate
    console.log("Participating in raffle:", raffle?.id);
    if (raffle) {
      setRaffle({ ...raffle, isParticipated: true });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!raffle) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load raffle data. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const renderCountdown = () => {
    if (timeLeft.completed) {
      return <span className="text-destructive">Event Ended</span>;
    }

    return (
      <div className="flex gap-2 items-center">
        <div className="flex flex-col items-center">
          <span className="text-2xl font-bold">{timeLeft.days}</span>
          <span className="text-xs text-muted-foreground">Days</span>
        </div>
        <span className="text-xl font-bold">:</span>
        <div className="flex flex-col items-center">
          <span className="text-2xl font-bold">{timeLeft.hours}</span>
          <span className="text-xs text-muted-foreground">Hours</span>
        </div>
        <span className="text-xl font-bold">:</span>
        <div className="flex flex-col items-center">
          <span className="text-2xl font-bold">{timeLeft.minutes}</span>
          <span className="text-xs text-muted-foreground">Mins</span>
        </div>
        <span className="text-xl font-bold">:</span>
        <div className="flex flex-col items-center">
          <span className="text-2xl font-bold">{timeLeft.seconds}</span>
          <span className="text-xs text-muted-foreground">Secs</span>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-3xl font-bold">
                    {raffle.title}
                  </CardTitle>
                  <CardDescription className="mt-2">
                    {raffle.description}
                  </CardDescription>
                </div>
                <Badge variant="secondary" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  {renderCountdown()}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Gift className="h-5 w-5" />
                    Prizes
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {raffle.prizes.map((prize) => (
                      <Card key={prize.id} className="hover:shadow-md transition-shadow">
                        <CardHeader>
                          <div className="flex justify-between items-start">
                            <CardTitle>{prize.name}</CardTitle>
                            <Badge variant="outline">
                              {prize.quantity} available
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground">
                            {prize.description}
                          </p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <List className="h-5 w-5" />
                    Rules
                  </h3>
                  <ul className="space-y-2">
                    {raffle.rules.map((rule, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-primary font-bold">•</span>
                        <span>{rule}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <Separator />

                <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-muted-foreground" />
                    <span className="text-muted-foreground">
                      {raffle.participationCount} participants
                    </span>
                  </div>

                  {raffle.isParticipated ? (
                    <Button disabled variant="outline" className="w-full sm:w-auto">
                      Joined
                    </Button>
                  ) : (
                    <Button
                      onClick={handleParticipate}
                      className="w-full sm:w-auto"
                    >
                      Join Now
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          {raffle.isAdmin && (
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Admin Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button variant="outline" className="w-full gap-2">
                  <Edit className="h-4 w-4" />
                  Edit Event
                </Button>
                <Button variant="outline" className="w-full gap-2">
                  <List className="h-4 w-4" />
                  View Results
                </Button>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Event Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Start Date</span>
                  <span>
                    {new Date(raffle.startTime).toLocaleDateString()}
                  </span>
                </div>
                <Progress
                  value={50} // This would be calculated based on time in a real app
                  className="h-2"
                />
                <div className="flex justify-between text-sm">
                  <span>End Date</span>
                  <span>{new Date(raffle.endTime).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}