"use client";
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PartyPopper } from 'lucide-react';
import { toast } from 'sonner';

export default function RaffleResultPage() {
  const router = useRouter();
  const { raffleId } = router.query;
  
  const [isLoading, setIsLoading] = useState(true);
  const [result, setResult] = useState<{
    isWinner: boolean;
    prize?: string;
    prizeImage?: string;
    raffleName: string;
  } | null>(null);

  useEffect(() => {
    // Simulate API call to get raffle result
    const fetchResult = async () => {
      try {
        // In a real app, this would be an actual API call
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Mock data - replace with actual API response
        const mockResult = {
          isWinner: Math.random() > 0.5,
          prize: Math.random() > 0.5 ? 'iPhone 15 Pro' : 'Amazon Gift Card',
          prizeImage: '',
          raffleName: 'Summer Giveaway'
        };
        
        setResult(mockResult);
        setIsLoading(false);
        
        if (mockResult.isWinner) {
          toast.success('Congratulations! You won a prize!');
        }
      } catch (error) {
        console.error('Failed to fetch raffle result:', error);
        setIsLoading(false);
        toast.error('Failed to load raffle result');
      }
    };
    
    fetchResult();
  }, [raffleId]);

  const handleBackToRaffle = () => {
    router.push(`/public/raffles/${raffleId}`);
  };

  const handleViewPrizes = () => {
    router.push('/account/prizes');
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-background to-muted p-4">
      <Card className="w-full max-w-md p-6 space-y-6 text-center">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary" />
            <p className="text-muted-foreground">Processing your entry...</p>
          </div>
        ) : result ? (
          <>
            {result.isWinner ? (
              <div className="relative">
                <div className="absolute -top-8 -right-8">
                  <PartyPopper className="h-16 w-16 text-yellow-500" />
                </div>
                <Badge variant="default" className="mb-4">
                  Winner!
                </Badge>
                <h1 className="text-2xl font-bold text-primary">Congratulations!</h1>
                <p className="text-muted-foreground">You won in the {result.raffleName}!</p>
                
                <div className="mt-6 p-4 bg-secondary/50 rounded-lg">
                  <p className="font-medium">Your Prize:</p>
                  <p className="text-xl font-bold text-primary mt-2">{result.prize}</p>
                </div>
                
                <p className="mt-4 text-sm text-muted-foreground">
                  Prize details will be sent to your registered email.
                </p>
              </div>
            ) : (
              <div>
                <Badge variant="outline" className="mb-4">
                  Result
                </Badge>
                <h1 className="text-2xl font-bold">Thank you for participating!</h1>
                <p className="text-muted-foreground mt-2">
                  Unfortunately, you didn't win this time in the {result.raffleName}.
                </p>
                <p className="mt-4 text-sm text-muted-foreground">
                  Better luck next time!
                </p>
              </div>
            )}
            
            <div className="flex flex-col space-y-2 mt-8">
              <Button onClick={handleBackToRaffle} variant="outline">
                Back to Raffle
              </Button>
              {result.isWinner && (
                <Button onClick={handleViewPrizes}>
                  View My Prize
                </Button>
              )}
            </div>
          </>
        ) : (
          <div className="py-12 text-center">
            <p className="text-muted-foreground">Failed to load raffle result</p>
            <Button onClick={handleBackToRaffle} variant="outline" className="mt-4">
              Back to Raffle
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
}
