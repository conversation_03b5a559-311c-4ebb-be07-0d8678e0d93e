"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Plus, Search } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

type Raffle = {
  id: string;
  title: string;
  description: string;
  endDate: Date;
  status: 'active' | 'upcoming' | 'ended';
  participants: number;
};

export default function RaffleListPage() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  const [raffles, setRaffles] = useState<Raffle[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isAdmin, setIsAdmin] = useState(false);

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(name, value);
      return params.toString();
    },
    [searchParams]
  );

  useEffect(() => {
    // Mock admin status
    setIsAdmin(true);
    
    // Mock data fetch
    const fetchRaffles = async () => {
      setLoading(true);
      try {
        // In a real app, this would be an API call with pagination and filtering
        const mockRaffles: Raffle[] = [
          {
            id: '1',
            title: 'Summer Giveaway',
            description: 'Win amazing summer essentials in our seasonal giveaway!',
            endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 3), // 3 days from now
            status: 'active',
            participants: 324,
          },
          {
            id: '2',
            title: 'Tech Gadgets Raffle',
            description: 'Chance to win the latest tech gadgets',
            endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1), // 1 day from now
            status: 'active',
            participants: 189,
          },
          {
            id: '3',
            title: 'Holiday Special',
            description: 'Festive season special prizes for everyone',
            endDate: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
            status: 'ended',
            participants: 512,
          },
          {
            id: '4',
            title: 'New Year Celebration',
            description: 'Start your new year with amazing prizes',
            endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 10), // 10 days from now
            status: 'upcoming',
            participants: 0,
          },
        ];

        // Apply filters
        let filtered = mockRaffles;
        if (filterStatus !== 'all') {
          filtered = filtered.filter(r => r.status === filterStatus);
        }
        if (searchQuery) {
          filtered = filtered.filter(r => 
            r.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
            r.description.toLowerCase().includes(searchQuery.toLowerCase())
          );
        }

        // Apply pagination (mock implementation)
        const itemsPerPage = 4;
        const startIndex = (currentPage - 1) * itemsPerPage;
        const paginated = filtered.slice(startIndex, startIndex + itemsPerPage);
        
        setRaffles(paginated);
        setTotalPages(Math.ceil(filtered.length / itemsPerPage));
      } catch (error) {
        console.error('Failed to fetch raffles:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRaffles();
  }, [currentPage, filterStatus, searchQuery]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active': return 'default';
      case 'upcoming': return 'secondary';
      case 'ended': return 'outline';
      default: return 'default';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">All Raffles</h1>
        {isAdmin && (
          <Button asChild>
            <Link href="/public/raffles/create">
              <Plus className="mr-2 h-4 w-4" /> Create New Raffle
            </Link>
          </Button>
        )}
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search raffles..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </form>

        <div className="w-full md:w-48">
          <Select 
            value={filterStatus} 
            onValueChange={(value) => {
              setFilterStatus(value);
              setCurrentPage(1);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="upcoming">Upcoming</SelectItem>
              <SelectItem value="ended">Ended</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : raffles.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500">No raffles found matching your criteria.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {raffles.map((raffle) => (
            <Link key={raffle.id} href={`/public/raffles/${raffle.id}`} className="hover:shadow-lg transition-shadow">
              <Card className="h-full flex flex-col">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg line-clamp-2">{raffle.title}</CardTitle>
                    <Badge variant={getStatusBadgeVariant(raffle.status)} className="shrink-0">
                      {raffle.status.charAt(0).toUpperCase() + raffle.status.slice(1)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="flex-1">
                  <p className="text-gray-600 dark:text-gray-400 line-clamp-3 mb-4">{raffle.description}</p>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-1 mb-1">
                      <span className="font-medium">Ends:</span>
                      <span>{formatDate(raffle.endDate)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span className="font-medium">Participants:</span>
                      <span>{raffle.participants}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between items-center">
                  <Button size="sm" variant="outline">View Details</Button>
                  {raffle.status === 'active' && (
                    <Badge variant="default">Join Now</Badge>
                  )}
                </CardFooter>
              </Card>
            </Link>
          ))}
        </div>
      )}

      {totalPages > 1 && (
        <div className="flex justify-center mt-8 gap-2">
          <Button 
            variant="outline" 
            size="sm"
            disabled={currentPage === 1}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={currentPage === page ? "default" : "outline"}
              size="sm"
              onClick={() => setCurrentPage(page)}
            >
              {page}
            </Button>
          ))}
          
          <Button 
            variant="outline" 
            size="sm"
            disabled={currentPage === totalPages}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
