{"name": "omni-js", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/material-nextjs": "^7.1.0", "@mui/x-date-pickers": "^8.5.0", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "dayjs": "^1.11.13", "howler": "^2.2.4", "js-cookie": "^3.0.5", "lucide-react": "^0.511.0", "moment": "^2.30.1", "next": "15.3.3", "react": "^19", "react-dom": "^19", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "recorder-core": "^1.3.25011100", "rehype-external-links": "^3.0.0", "remark-gfm": "^4.0.1"}}