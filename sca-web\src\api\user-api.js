import api from "@/core/api/api";

export const userApi = {
  /**
   * 用户登录
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @returns {Promise<{access_token: string}>} 登录结果
   */
  login: async (username, password) => {
      return await api({
        resource: "user",
        method_name: "login",
        username,
        password,
    });
  },

  /**
   * 查询所有用户
   * @param {number} page - 页码
   * @param {number} pageSize - 每页条数
   * @param {string} searchTerm - 搜索关键词
   * @returns {Promise<{data: Array, total: number}>} 用户列表
   */
  queryAll: async (page, pageSize, searchTerm = "") => {
    return await api({
      resource: "user",
      method_name: "query_all",
      page,
      pageSize,
      searchTerm,
    });
  },

  /**
   * 删除用户
   * @param {string} id_ - 用户ID
   * @returns {Promise<{message: string}>} 删除结果
   */
  deleteUser: async (id_) => {
    return await api({
      resource: "user",
      method_name: "delete",
      id_,
    });
  },

  /**
   * 用户注册
   * @param {Object} formData - 注册表单数据
   * @param {string} formData.phone - 手机号码
   * @param {string} formData.password - 密码
   * @returns {Promise<{success: boolean, message: string}>} 注册结果
   */
  register: async (formData) => {
    return await api({
      resource: "user",
      method_name: "register",
      phone: formData.phone,
      password: formData.password,
    });
  },

  /**
   * 获取当前用户角色
   * @returns {Promise<{roles: string[]}>} 用户角色列表
   */
  getRoles: async () => {
    return await api({
      resource: "user",
      method_name: "get_roles",
    });
  },
};