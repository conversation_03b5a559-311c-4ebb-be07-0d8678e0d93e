"use client";

import { 
  Box, 
  Typography, 
  Card, 
  CardContent, 
  CardMedia, 
  Grid, 
  Chip, 
  Divider, 
  Button,
  Container,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { ArrowRight } from 'lucide-react';
import { InfiniteScrollList } from '@/core/components/InfiniteScrollList';
import { useState, useCallback } from 'react';

// 模拟数据
const allCasesData = [
  {
    id: 1,
    title: "智能教育平台",
    type: "教育软件",
    description: "基于AI的智能教育平台，提供个性化学习路径和智能辅导功能",
    imageUrl: "https://placehold.co/600x400/e9f5fe/156ac9?text=Education",
    tags: ["AI", "教育", "个性化"]
  },
  {
    id: 2,
    title: "企业资源管理系统",
    type: "管理软件",
    description: "全面的企业资源规划系统，整合人力资源、财务、供应链等模块",
    imageUrl: "https://placehold.co/600x400/f5f5f5/666666?text=ERP",
    tags: ["ERP", "企业管理", "资源规划"]
  },
  {
    id: 3,
    title: "智慧医疗平台",
    type: "医疗软件",
    description: "连接医院、医生和患者的智慧医疗平台，提供远程诊疗和健康管理",
    imageUrl: "https://placehold.co/600x400/e6f7ee/2e7d52?text=Medical",
    tags: ["医疗", "远程诊疗", "健康管理"]
  },
  {
    id: 4,
    title: "智能客服系统",
    type: "服务软件",
    description: "基于自然语言处理的智能客服系统，提供24小时自动化客户服务",
    imageUrl: "https://placehold.co/600x400/fff8e1/f57c00?text=Service",
    tags: ["NLP", "客服", "自动化"]
  }
];

export default function CopyrightCases() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [page, setPage] = useState(1);
  const [cases, setCases] = useState(allCasesData);
  const [hasMore, setHasMore] = useState(true);
  
  // 模拟加载更多数据
  const loadMore = useCallback(async () => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟加载更多数据
    if (page < 3) {
      const newCases = allCasesData.map(item => ({
        ...item,
        id: item.id + page * allCasesData.length
      }));
      
      setCases(prevCases => [...prevCases, ...newCases]);
      setPage(prevPage => prevPage + 1);
    } else {
      setHasMore(false);
    }
  }, [page]);
  
  // 渲染单个案例卡片
  const renderCaseCard = (caseItem) => (
            <Card 
              sx={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column',
                borderRadius: 2,
                boxShadow: 1,
                transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 3
                }
              }}
            >
              <Box sx={{ position: 'relative', paddingTop: '56.25%', width: '100%' }}>
                <CardMedia
                  component="img"
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                  image={caseItem.imageUrl}
                  alt={caseItem.title}
                />
              </Box>
              <CardContent sx={{ flexGrow: 1, p: { xs: 2, md: 2.5 } }}>
                <Typography 
                  variant={isMobile ? "subtitle1" : "h6"} 
                  component="h2" 
                  sx={{ fontWeight: 600, mb: 1 }}
                >
                  {caseItem.title}
                </Typography>
                <Divider sx={{ my: 1 }} />
                <Button 
                  endIcon={<ArrowRight size={16} />}
                  sx={{ 
                    mt: 1, 
                    textTransform: 'none',
                    justifyContent: 'flex-start',
                    pl: 0,
                    '&:hover': {
                      bgcolor: 'transparent',
                      color: 'primary.main'
                    }
                  }}
                >
                  查看详情
                </Button>
              </CardContent>
            </Card>
  );
  
  return (
    <Container maxWidth="md" sx={{ py: { xs: 2, md: 4 } }}>
      <Typography 
        variant={isMobile ? "h5" : "h4"} 
        component="h1" 
        sx={{ mb: 1, fontWeight: 600 }}
      >
        软著案例展示
      </Typography>
      <Typography 
        variant="body1" 
        color="text.secondary" 
        sx={{ mb: { xs: 2, md: 4 } }}
      >
        浏览成功申请软件著作权的案例，获取灵感和参考
      </Typography>
      
      <InfiniteScrollList
        items={cases}
        renderItem={renderCaseCard}
        loadMore={loadMore}
        hasMore={hasMore}
        gridTemplateColumns={{
          xs: "1fr",
          sm: "1fr 1fr",
          md: "1fr 1fr 1fr"
        }}
      />
    </Container>
  );
} 