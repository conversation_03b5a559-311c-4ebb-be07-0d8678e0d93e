"use client";

import { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Container,
  TextField, 
  Button, 
  Grid,
  Alert,
  Stack,
  useTheme,
  useMediaQuery,
  Paper,
  Chip,
  Tooltip,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import { useRouter } from 'next/navigation';
import { Save, ArrowLeft, FileText, Info, Sparkles, Key } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType } from "@/core/components/alert";
import { copyrightTaskApi } from "@/api/copyright-task-api";

export default function CreateCopyrightTask() {
  const router = useRouter();
  const dispatch = useDispatch();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  
  // 表单错误状态
  const [errors, setErrors] = useState({});
  
  // AI扩写状态
  const [isExpanding, setIsExpanding] = useState(false);
  
  // 处理输入变化
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    
    // 清除错误
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };
  
  // 表单验证
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = '请输入任务名称';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = '请输入需求描述';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // 处理表单提交
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      try {
        // 提交表单到API
        await copyrightTaskApi.create(formData.name, formData.description, formData.authorization_code);
        
        // 显示成功通知
        dispatch(addAlert({ type: AlertType.SUCCESS, message: "任务创建成功" }));
        
        // 跳转到任务列表页面
        router.push('/protected/copyright-tasks');
      } catch (error) {
        console.error('提交任务失败:', error);
        dispatch(addAlert({ 
          type: AlertType.ERROR, 
          message: error.message || "创建任务失败，请稍后重试" 
        }));
      }
    }
  };
  
  // AI扩写功能
  const handleAIExpand = async () => {
    // 验证是否有基础内容
    if (!formData.description.trim()) {
      setErrors({
        ...errors,
        description: '请先输入基本描述，AI将帮您扩展完善'
      });
      return;
    }
    
    setIsExpanding(true);
    
    try {
      // 调用AI扩展描述API
      const result = await copyrightTaskApi.aiExpandDescription(formData.description);
      
      // 更新表单数据
      setFormData({
        ...formData,
        description: result.expanded_description
      });
      
      dispatch(addAlert({ type: AlertType.SUCCESS, message: "AI已帮您扩展完善描述" }));
    } catch (error) {
      console.error('AI扩写出错:', error);
      dispatch(addAlert({ 
        type: AlertType.ERROR, 
        message: error.message || "AI扩展失败，请稍后重试" 
      }));
    } finally {
      setIsExpanding(false);
    }
  };
  
  return (
    <Container maxWidth="md" sx={{ py: { xs: 2, md: 4 } }}>
      <Button
        startIcon={<ArrowLeft size={16} />}
        onClick={() => router.push('/protected/copyright-tasks')}
        sx={{ 
          mb: 3, 
          textTransform: 'none',
          color: 'text.secondary',
          '&:hover': {
            backgroundColor: 'action.hover',
          }
        }}
      >
        返回任务列表
      </Button>
      
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant={isMobile ? "h5" : "h4"} 
          component="h1" 
          sx={{ 
            fontWeight: 700,
            mb: 1
          }}
        >
          创建软著任务
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
        >
          只需描述您的软著需求，我们会为您处理后续流程
        </Typography>
      </Box>

      <Paper 
        elevation={0} 
        sx={{ 
          borderRadius: 3,
          overflow: 'hidden',
          border: '1px solid',
          borderColor: theme.palette.divider,
          mb: 4
        }}
      >
        <Box sx={{ 
          p: { xs: 2, md: 3 }, 
          background: `linear-gradient(45deg, ${theme.palette.primary.light}, ${theme.palette.primary.lighter})`,
          borderBottom: '1px solid',
          borderColor: theme.palette.divider,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Stack direction="row" spacing={1.5} alignItems="center">
            <Box 
              sx={{
                backgroundColor: 'white',
                borderRadius: '50%',
                width: 32,
                height: 32,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <FileText size={18} color={theme.palette.primary.main} />
            </Box>
            <Typography variant="subtitle1" fontWeight={600} color="primary.dark">
              需求描述
            </Typography>
            <Chip 
              label="必填" 
              size="small" 
              color="primary" 
              sx={{ 
                height: 20, 
                fontSize: '0.7rem',
                backgroundColor: 'white',
                fontWeight: 500
              }}
            />
          </Stack>
          
          <Tooltip title="详细的需求描述有助于我们更好地理解您的软件，提高申请效率">
            <Info size={18} color={theme.palette.primary.dark} />
          </Tooltip>
        </Box>
        
        <Box sx={{ p: { xs: 2, md: 3 } }}>
          <form onSubmit={handleSubmit}>
            <Alert 
              severity="info" 
              icon={<Info size={18} />}
              sx={{ 
                mb: 3, 
                borderRadius: 2,
                backgroundColor: 'rgba(3, 169, 244, 0.1)',
                color: 'info.dark',
                border: '1px solid',
                borderColor: 'info.light',
                '& .MuiAlert-message': {
                  fontSize: '0.9rem'
                }
              }}
            >
              详细的需求描述有助于我们更好地理解您的软件，提高申请效率。您可以使用AI扩写功能完善描述。
            </Alert>
            
            <TextField
              fullWidth
              label="任务名称"
              placeholder="请输入任务名称"
              name="name"
              value={formData.name}
              onChange={handleChange}
              error={!!errors.name}
              helperText={errors.name}
              required
              variant="outlined"
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'background.paper',
                  transition: 'all 0.3s',
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: '1px',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: '2px',
                    boxShadow: `0 0 0 3px ${theme.palette.primary.lighter}`
                  }
                }
              }}
            />
            
            <FormControl 
              fullWidth 
              error={!!errors.authorization_code}
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'background.paper',
                  transition: 'all 0.3s',
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: '1px',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: '2px',
                    boxShadow: `0 0 0 3px ${theme.palette.primary.lighter}`
                  }
                }
              }}
            >
              <TextField
                fullWidth
                label="授权码"
                placeholder="请输入授权码"
                name="authorization_code"
                value={formData.authorization_code}
                onChange={handleChange}
                error={!!errors.authorization_code}
                helperText={errors.authorization_code || "请输入您的授权码"}
                required
                variant="outlined"
                InputProps={{
                  startAdornment: <Key size={18} style={{ marginRight: 8 }} />
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: 'background.paper',
                  }
                }}
              />
            </FormControl>
            
            <TextField
              fullWidth
              placeholder="请简要描述您的软件功能、用途、特点等信息，可使用AI功能辅助扩展完善..."
              name="description"
              value={formData.description}
              onChange={handleChange}
              multiline
              rows={isMobile ? 10 : 12}
              error={!!errors.description}
              helperText={errors.description}
              required
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'background.paper',
                  transition: 'all 0.3s',
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: '1px',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: '2px',
                    boxShadow: `0 0 0 3px ${theme.palette.primary.lighter}`
                  }
                }
              }}
            />
            
            <Stack 
              direction={isMobile ? "column" : "row"} 
              justifyContent="space-between" 
              alignItems={isMobile ? "flex-start" : "center"} 
              spacing={isMobile ? 2 : 0}
              sx={{ mt: 2 }}
            >
              <Typography 
                variant="caption" 
                color="text.secondary" 
                sx={{ ml: isMobile ? 0 : 1 }}
              >
                我们会根据您的描述为您准备软著申请所需材料
              </Typography>
              
              <Button
                variant="outlined"
                color="primary"
                size="medium"
                onClick={handleAIExpand}
                disabled={isExpanding}
                startIcon={isExpanding ? <CircularProgress size={16} /> : <Sparkles size={16} />}
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  borderWidth: '1px',
                  px: 2,
                  py: 1,
                  backgroundColor: isExpanding ? 'transparent' : 'rgba(25, 118, 210, 0.08)',
                  '&:hover': {
                    borderWidth: '1px',
                    backgroundColor: 'rgba(25, 118, 210, 0.12)'
                  }
                }}
              >
                {isExpanding ? '智能完善中...' : 'AI智能完善'}
              </Button>
            </Stack>
            
            <Grid container justifyContent="center" sx={{ mt: 4 }}>
              <Grid item xs={12} md={6}>
                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  startIcon={<Save size={isMobile ? 16 : 18} />}
                  sx={{ 
                    textTransform: 'none', 
                    borderRadius: 2,
                    py: 1.5,
                    boxShadow: '0 8px 16px rgba(25, 118, 210, 0.15)',
                    transition: 'all 0.3s',
                    '&:hover': {
                      boxShadow: '0 12px 20px rgba(25, 118, 210, 0.2)',
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  提交任务
                </Button>
              </Grid>
            </Grid>
          </form>
        </Box>
      </Paper>
    </Container>
  );
} 