"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  Typography,
  <PERSON>,
  Button,
  Stack,
  Grid,
  Card,
  CardContent,
  useTheme,
} from "@mui/material";
import { PlusCircle, FileText, RefreshCw, ExternalLink } from "lucide-react";
import { useRouter } from "next/navigation";
import { InfiniteScrollList } from "@/core/components/InfiniteScrollList";
import { copyrightTaskApi } from "@/api/copyright-task-api";

// 状态颜色映射
const statusColorMap = {
  生成中: "warning",
  已完成: "success",
  生成失败: "error",
};

export default function CopyrightTasks() {
  const [displayedTasks, setDisplayedTasks] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const [isRefreshingAll, setIsRefreshingAll] = useState(false);
  const router = useRouter();
  const theme = useTheme();

  // 格式化日期时间戳为可读格式
  const formatDate = (timestamp) => {
    const date = new Date(timestamp * 1000);
    return date.toISOString().split('T')[0];
  };

  // 加载任务数据
  const loadTaskData = async () => {
    try {
      const response = await copyrightTaskApi.queryList({ page: 1 });
      setDisplayedTasks(response.data || []);
      setHasMore(response.total > response.data.length);
      setPage(1);
    } catch (error) {
      console.error('加载任务列表失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    // 加载第一页数据
    loadTaskData();
  }, []);

  // 加载更多数据
  const loadMore = async () => {
    const nextPage = page + 1;
    
    try {
      const response = await copyrightTaskApi.queryList({ 
        page: nextPage
      });
      
      const newItems = response.data || [];
      
      if (newItems.length > 0) {
        setDisplayedTasks((prevTasks) => [...prevTasks, ...newItems]);
        setPage(nextPage);
      }
      
      // 检查是否还有更多数据可加载
      setHasMore(response.total > (displayedTasks.length + newItems.length));
      
      return Promise.resolve();
    } catch (error) {
      console.error('加载更多数据失败:', error);
      return Promise.reject(error);
    }
  };

  const handleCreateTask = () => {
    router.push("/protected/copyright-tasks/create");
  };

  // 刷新所有任务
  const handleRefreshAll = async () => {
    setIsRefreshingAll(true);
    
    try {
      await loadTaskData();
    } catch (error) {
      console.error('刷新所有任务失败:', error);
    } finally {
      setIsRefreshingAll(false);
    }
  };

  // 处理文档下载
  const handleDownloadDocument = (task) => {
    if (task.download_url) {
      // 直接打开下载链接
      window.open(task.download_url, '_blank');
    } else {
        console.error('无法获取文档下载链接, 请刷新进度后重试');
    }
  };

  // 任务卡片组件
  const TaskCard = ({ task }) => (
    <Card
      sx={{
        height: "100%",
        width: "100%",
        borderRadius: 3,
        border: "none",
        boxShadow: "0 2px 12px rgba(0,0,0,0.05)",
        transition: "transform 0.2s, box-shadow 0.2s",
        "&:hover": {
          transform: "translateY(-4px)",
          boxShadow: "0 8px 16px rgba(0,0,0,0.1)",
        },
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Stack spacing={2}>
          <Typography variant="h6" fontWeight={600} gutterBottom>
            {task.name}
          </Typography>
          
          <Stack direction="row" spacing={1} alignItems="center">
            <Chip
              label={task.status}
              color={statusColorMap[task.status] || "default"}
              variant="filled"
              size="small"
              sx={{ fontWeight: 500 }}
            />
            <Chip
              label={formatDate(task.create_time)}
              variant="outlined"
              size="small"
              color="default"
              sx={{ fontWeight: 500 }}
            />
          </Stack>

          {task.status === "生成中" && (
            <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
              <Box
                sx={{
                  width: "100%",
                  position: "relative",
                  height: 16,
                  backgroundColor: theme.palette.grey[100],
                  borderRadius: 8,
                  overflow: "hidden",
                  boxShadow: "inset 0 2px 4px rgba(0,0,0,0.1)",
                }}
              >
                <Box
                  sx={{
                    width: `${task.progress || 0}%`,
                    height: "100%",
                    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
                    borderRadius: 8,
                    transition: "width 0.5s ease-in-out",
                    boxShadow: "0 0 10px rgba(0,132,255,0.5)",
                    animation: "pulse 1.5s infinite",
                    "@keyframes pulse": {
                      "0%": { boxShadow: "0 0 5px rgba(0,132,255,0.2)" },
                      "50%": { boxShadow: "0 0 15px rgba(0,132,255,0.4)" },
                      "100%": { boxShadow: "0 0 5px rgba(0,132,255,0.2)" },
                    },
                  }}
                />
                <Typography 
                  variant="body2" 
                  sx={{ 
                    position: "absolute", 
                    right: 10, 
                    top: "50%", 
                    transform: "translateY(-50%)",
                    color: "text.secondary",
                    fontWeight: 600,
                    zIndex: 2,
                  }}
                >
                  {task.progress || 0}%
                </Typography>
              </Box>
            </Box>
          )}

          <Stack direction="row" spacing={1} justifyContent="flex-end" sx={{ mt: 1 }}>
            {task.status === "已完成" && task.document_key && (
              <Button
                size="small"
                color="primary"
                variant="contained"
                startIcon={<FileText size={16} />}
                onClick={() => handleDownloadDocument(task)}
                sx={{ 
                  textTransform: 'none',
                  borderRadius: 20,
                  boxShadow: 'none',
                  px: 2
                }}
              >
                下载文档
              </Button>
            )}
            {task.status === "已完成" && task.deployment_url && (
              <Button
                size="small"
                color="primary"
                variant="outlined"
                startIcon={<ExternalLink size={16} />}
                onClick={() => window.open(task.deployment_url, "_blank")}
                sx={{
                  textTransform: "none",
                  borderRadius: 20,
                  px: 2,
                }}
              >
                访问应用
              </Button>
            )}
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Box
      sx={{ width: "100%", maxWidth: 1200, mx: "auto", px: { xs: 2, md: 0 } }}
    >
      <Box sx={{ mb: 3 }}>
        <Box>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              mb: 1,
              fontWeight: 600,
              fontSize: { xs: "1.5rem", md: "2rem" },
            }}
          >
            软著任务列表
          </Typography>
          <Typography 
            variant="body1" 
            color="text.secondary"
            sx={{ mb: 2 }}
          >
            管理您的软件著作权申请任务
          </Typography>
        </Box>
        <Stack 
          direction={{ xs: 'column', sm: 'row' }} 
          spacing={2} 
          alignItems={{ xs: 'flex-start', sm: 'center' }}
        >
          <Button
            variant="contained"
            startIcon={<PlusCircle size={18} />}
            onClick={handleCreateTask}
            sx={{ 
              borderRadius: 20, 
              textTransform: "none", 
              py: 1,
              px: 2,
              whiteSpace: "nowrap",
              minWidth: { xs: "100%", sm: "120px" }
            }}
          >
            创建任务
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshCw size={18} />}
            onClick={handleRefreshAll}
            disabled={isRefreshingAll}
            sx={{ 
              borderRadius: 20, 
              textTransform: "none", 
              py: 1,
              px: 2,
              minWidth: { xs: "100%", sm: "120px" }
            }}
          >
            刷新进度
          </Button>
        </Stack>
      </Box>

      <InfiniteScrollList
        items={displayedTasks}
        renderItem={(task) => <TaskCard task={task} />}
        loadMore={loadMore}
        hasMore={hasMore}
      />
    </Box>
  );
}
