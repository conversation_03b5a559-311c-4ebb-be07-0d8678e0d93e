"use client";

import { Box, Card, CardContent, Typography, Grid, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { TrendingUp, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { <PERSON><PERSON>hart, Line, <PERSON><PERSON><PERSON>, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

// Mock数据 (7月当前数据)
const mockData = {
    totalGenerated: 2312,
    certified: 1965,
    pending: 277,
    needCorrection: 70
};

const monthlyTrendData = [
    { month: '3月', generated: 311, certified: 264 },
    { month: '4月', generated: 611, certified: 519 },
    { month: '5月', generated: 823, certified: 700 },
    { month: '6月', generated: 2133, certified: 1813 },
    { month: '7月', generated: 2312, certified: 1965 }
];

const statusDistributionData = [
    { name: '已下证', value: mockData.certified, color: '#4CAF50' },
    { name: '待审核', value: mockData.pending, color: '#FF9800' },
    { name: '待补正', value: mockData.needCorrection, color: '#F44336' }
];

// 统计卡片组件
const StatCard = ({ title, value, icon, color, subtitle }) => {
    const theme = useTheme();
    
    return (
        <Card
            sx={{
                height: '100%',
                borderRadius: 3,
                border: `1px solid ${theme.palette.divider}`,
                boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
                '&:hover': {
                    boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
                    transform: 'translateY(-2px)'
                },
                transition: 'all 0.3s ease'
            }}
        >
            <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box
                        sx={{
                            p: 1.5,
                            borderRadius: 2,
                            backgroundColor: `${color}10`,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}
                    >
                        {icon}
                    </Box>
                </Box>
                <Typography
                    variant="h3"
                    sx={{
                        fontWeight: 700,
                        color: theme.palette.text.primary,
                        mb: 0.5,
                        fontSize: '2rem'
                    }}
                >
                    {value.toLocaleString()}
                </Typography>
                <Typography
                    variant="body1"
                    sx={{
                        color: theme.palette.text.secondary,
                        fontWeight: 500,
                        mb: 1
                    }}
                >
                    {title}
                </Typography>
                {subtitle && (
                    <Typography
                        variant="caption"
                        sx={{
                            color: theme.palette.text.disabled,
                            fontSize: '0.75rem'
                        }}
                    >
                        {subtitle}
                    </Typography>
                )}
            </CardContent>
        </Card>
    );
};

// 图表卡片组件
const ChartCard = ({ title, children, height = 350 }) => {
    const theme = useTheme();
    
    return (
        <Paper
            sx={{
                p: 3,
                borderRadius: 3,
                border: `1px solid ${theme.palette.divider}`,
                boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
                height: height
            }}
        >
            <Typography
                variant="h6"
                sx={{
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                    mb: 3
                }}
            >
                {title}
            </Typography>
            <Box sx={{ height: height - 100 }}>
                {children}
            </Box>
        </Paper>
    );
};

// 趋势图表组件
const TrendChart = ({ data }) => {
    const theme = useTheme();
    
    return (
        <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                <XAxis
                    dataKey="month"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                />
                <YAxis
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                />
                <Tooltip
                    contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 8,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                    }}
                />
                <Legend />
                <Line
                    type="monotone"
                    dataKey="generated"
                    stroke={theme.palette.primary.main}
                    strokeWidth={3}
                    dot={{ r: 4, fill: theme.palette.primary.main }}
                    name="生成数量"
                />
                <Line
                    type="monotone"
                    dataKey="certified"
                    stroke="#4CAF50"
                    strokeWidth={3}
                    dot={{ r: 4, fill: "#4CAF50" }}
                    name="下证数量"
                />
            </LineChart>
        </ResponsiveContainer>
    );
};

// 状态饼图组件
const StatusPieChart = ({ data }) => {
    const theme = useTheme();
    
    return (
        <ResponsiveContainer width="100%" height="100%">
            <PieChart>
                <Pie
                    data={data}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                >
                    {data.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                </Pie>
                <Tooltip
                    contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 8,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                    }}
                />
                <Legend />
            </PieChart>
        </ResponsiveContainer>
    );
};

// 主页面组件
export default function StatisticsPage() {
    const theme = useTheme();
    
    return (
        <Box sx={{ p: 0 }}>
            {/* 页面标题 */}
            <Box sx={{ mb: 4 }}>
                <Typography
                    variant="h4"
                    sx={{
                        fontWeight: 700,
                        color: theme.palette.text.primary,
                        mb: 1
                    }}
                >
                    数据统计
                </Typography>
                <Typography
                    variant="body1"
                    sx={{ color: theme.palette.text.secondary }}
                >
                    软件著作权申请数据概览与分析
                </Typography>
            </Box>

            {/* 统计卡片区域 */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
                    <StatCard
                        title="本月生成软著"
                        value={mockData.totalGenerated}
                        icon={<TrendingUp size={24} color={theme.palette.primary.main} />}
                        color={theme.palette.primary.main}
                        subtitle="较上月增长 15.2%"
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
                    <StatCard
                        title="已下证"
                        value={mockData.certified}
                        icon={<CheckCircle size={24} color="#4CAF50" />}
                        color="#4CAF50"
                        subtitle="下证率 85.0%"
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
                    <StatCard
                        title="待审核"
                        value={mockData.pending}
                        icon={<Clock size={24} color="#FF9800" />}
                        color="#FF9800"
                        subtitle="平均审核周期 45 天"
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
                    <StatCard
                        title="待补正"
                        value={mockData.needCorrection}
                        icon={<AlertTriangle size={24} color="#F44336" />}
                        color="#F44336"
                        subtitle="需要用户处理"
                    />
                </Grid>
            </Grid>

            {/* 图表区域 */}
            <Grid container spacing={3}>
                <Grid size={{ xs: 12, lg: 8 }}>
                    <ChartCard title="月度趋势分析" height={400}>
                        <TrendChart data={monthlyTrendData} />
                    </ChartCard>
                </Grid>
                <Grid size={{ xs: 12, lg: 4 }}>
                    <ChartCard title="状态分布" height={400}>
                        <StatusPieChart data={statusDistributionData} />
                    </ChartCard>
                </Grid>
            </Grid>
        </Box>
    );
}