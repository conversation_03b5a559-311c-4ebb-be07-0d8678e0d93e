"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Stack,
  Avatar,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { <PERSON>, EyeOff, Save, ArrowLeft } from "lucide-react";
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType } from "@/core/components/alert";

// 模拟用户数据
const mockUsers = [
  {
    id: "U001",
    username: "z<PERSON><PERSON>",
    email: "<EMAIL>",
    role: "管理员",
    lastLogin: "2023-11-15 14:30",
  },
  {
    id: "U002",
    username: "lisi",
    email: "<EMAIL>",
    role: "普通用户",
    lastLogin: "2023-11-14 09:45",
  },
  {
    id: "U003",
    username: "wangwu",
    email: "<EMAIL>",
    role: "普通用户",
    lastLogin: "2023-11-10 16:20",
  },
  {
    id: "U004",
    username: "z<PERSON><PERSON><PERSON>",
    email: "zhao<PERSON><PERSON>@example.com",
    role: "审批员",
    lastLogin: "2023-11-13 11:05",
  },
];

export default function EditUserPassword({ params }) {
  const userId = params.id;
  const router = useRouter();
  const dispatch = useDispatch();
  
  // 获取用户数据
  const user = mockUsers.find(u => u.id === userId) || {
    id: userId,
    username: "未知用户",
    email: "<EMAIL>",
    role: "未知"
  };
  
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordError, setPasswordError] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");
  
  const handlePasswordChange = (e) => {
    const value = e.target.value;
    setPassword(value);
    
    if (value.length < 8) {
      setPasswordError("密码长度至少为8个字符");
    } else {
      setPasswordError("");
    }
    
    if (confirmPassword && value !== confirmPassword) {
      setConfirmPasswordError("两次输入的密码不一致");
    } else {
      setConfirmPasswordError("");
    }
  };
  
  const handleConfirmPasswordChange = (e) => {
    const value = e.target.value;
    setConfirmPassword(value);
    
    if (value !== password) {
      setConfirmPasswordError("两次输入的密码不一致");
    } else {
      setConfirmPasswordError("");
    }
  };
  
  const handleBack = () => {
    router.back();
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (passwordError || confirmPasswordError) {
      return;
    }
    
    if (!password) {
      setPasswordError("请输入新密码");
      return;
    }
    
    if (!confirmPassword) {
      setConfirmPasswordError("请确认新密码");
      return;
    }
    
    // 在这里处理密码修改逻辑
    dispatch(addAlert({ type: AlertType.SUCCESS, message: "密码修改成功" }));
    
    // 返回用户列表页
    router.push("/protected/user");
  };
  
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
      <Box sx={{ width: '100%', maxWidth: '800px' }}>
        <Box sx={{ p: { xs: 2, md: 3 } }}>
          <Button
            startIcon={<ArrowLeft size={16} />}
            onClick={handleBack}
            sx={{ mb: 3 }}
          >
            返回
          </Button>
          
          <Typography variant="h5" component="h1" gutterBottom>
            修改用户密码
          </Typography>
          
          <Card sx={{ mt: 3, boxShadow: 'none', border: '1px solid', borderColor: 'divider' }}>
            <CardContent>
              <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 4 }}>
                <Avatar sx={{ width: 48, height: 48, bgcolor: 'primary.main' }}>
                  {user.username.charAt(0).toUpperCase()}
                </Avatar>
                <Box>
                  <Typography variant="h6">{user.username}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {user.email} · {user.role}
                  </Typography>
                </Box>
              </Stack>
              
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <TextField
                    label="新密码"
                    type={showPassword ? "text" : "password"}
                    fullWidth
                    value={password}
                    onChange={handlePasswordChange}
                    error={Boolean(passwordError)}
                    helperText={passwordError}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                          </IconButton>
                        </InputAdornment>
                      )
                    }}
                  />
                  
                  <TextField
                    label="确认新密码"
                    type={showConfirmPassword ? "text" : "password"}
                    fullWidth
                    value={confirmPassword}
                    onChange={handleConfirmPasswordChange}
                    error={Boolean(confirmPasswordError)}
                    helperText={confirmPasswordError}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            edge="end"
                          >
                            {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                          </IconButton>
                        </InputAdornment>
                      )
                    }}
                  />
                  
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                    <Button
                      variant="contained"
                      type="submit"
                      startIcon={<Save size={16} />}
                    >
                      保存修改
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Box>
      </Box>
    </Box>
  );
} 