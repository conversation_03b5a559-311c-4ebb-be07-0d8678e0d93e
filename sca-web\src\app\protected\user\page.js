"use client";

import { useState, useCallback, useEffect } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  TextField,
  Stack,
  Card,
  CardContent,
  Avatar,
  IconButton,
  Tooltip,
  Chip,
  CircularProgress,
} from "@mui/material";
import { Search, RefreshCw, Edit, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";
import { InfiniteScrollList } from "@/core/components/InfiniteScrollList";
import { userApi } from "@/api/user-api";

export default function UserManagement() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [users, setUsers] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const pageSize = 8;

  // 首次加载数据
  useEffect(() => {
    fetchUsers(1);
  }, []);

  // 获取用户数据
  const fetchUsers = async (pageNum, isRefresh = false) => {
    try {
      setLoading(true);
      const response = await userApi.queryAll(pageNum, pageSize, searchTerm);
      const { data, total } = response;
      
      let newUsersList;
      if (isRefresh || pageNum === 1) {
        newUsersList = data;
        setUsers(data);
      } else {
        newUsersList = [...users, ...data];
        setUsers(newUsersList);
      }
      
      // 判断是否还有更多数据，使用最新的用户列表长度
      setHasMore(newUsersList.length < total);
      setPage(pageNum);
    } catch (error) {
      dispatch(addAlert({ type: AlertType.ERROR, message: "获取用户数据失败" }));
      console.error("获取用户数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditUser = (userId) => {
    router.push(`/protected/user/edit/${userId}`);
  };

  const handleDeleteUser = async (userId) => {
    try {
      await userApi.deleteUser(userId);
      // 刷新数据
      fetchUsers(1, true);
      dispatch(addAlert({ type: AlertType.SUCCESS, message: AlertMsg.DELETE }));
    } catch (error) {
      dispatch(addAlert({ type: AlertType.ERROR, message: "删除用户失败" }));
      console.error("删除用户失败:", error);
    }
  };

  const handleRefresh = () => {
    setSearchTerm("");
    fetchUsers(1, true);
    dispatch(addAlert({ type: AlertType.INFO, message: "数据已刷新" }));
  };

  const handleSearchInputChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleSearchClick = () => {
    fetchUsers(1, true);
  };

  const loadMoreUsers = useCallback(async () => {
    if (loading) return;
    const nextPage = page + 1;
    await fetchUsers(nextPage);
  }, [page, loading, searchTerm]);

  const getRoleColor = (role) => {
    if (role.includes("管理员")) {
      return "primary";
    } else if (role.includes("审批员")) {
      return "info";
    } else {
      return "default";
    }
  };

  const renderUserCard = (user, index) => {
    return (
      <Card key={user.id_} sx={{ height: '100%', display: 'flex', flexDirection: 'column', boxShadow: 'none', border: '1px solid', borderColor: 'divider' }}>
        <CardContent sx={{ flexGrow: 1 }}>
          <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <Avatar sx={{ width: 48, height: 48, bgcolor: 'primary.main' }}>
              {user.username ? user.username.charAt(0).toUpperCase() : "U"}
            </Avatar>
            <Box>
              <Typography variant="h6">{user.username}</Typography>
            </Box>
          </Stack>
          
          <Stack spacing={1} sx={{ mb: 2 }}>
            <Stack direction="row" justifyContent="flex-end">
              <Chip 
                label={user.role} 
                color={getRoleColor(user.role)}
                variant="outlined" 
                size="small" 
              />
            </Stack>
          </Stack>
          
          <Stack direction="row" spacing={1} justifyContent="flex-end">
            <Tooltip title="修改用户">
              <IconButton 
                size="small" 
                color="primary" 
                onClick={() => handleEditUser(user.id_)}
              >
                <Edit size={18} />
              </IconButton>
            </Tooltip>
            <Tooltip title="删除用户">
              <IconButton 
                size="small" 
                color="error" 
                onClick={() => handleDeleteUser(user.id_)}
              >
                <Trash2 size={18} />
              </IconButton>
            </Tooltip>
          </Stack>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
      <Box sx={{ width: '100%', maxWidth: '1200px' }}>
        <Box sx={{ p: { xs: 2, md: 3 } }}>
          <Typography variant="h5" component="h1" gutterBottom>
            用户管理
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            管理系统用户账号、权限和密码
          </Typography>
          
          <Stack 
            direction={{ xs: "column", md: "row" }} 
            spacing={2} 
            sx={{ mb: 3, mt: 2 }}
            justifyContent="space-between"
            alignItems={{ xs: "stretch", md: "center" }}
          >
            <Stack direction="row" spacing={1}>
              <TextField
                placeholder="搜索用户..."
                size="small"
                value={searchTerm}
                onChange={handleSearchInputChange}
              />
              <IconButton onClick={handleSearchClick} size="small">
                <Search size={18} />
              </IconButton>
              <Tooltip title="刷新">
                <IconButton onClick={handleRefresh} size="small" disabled={loading}>
                  {loading ? (
                    <CircularProgress size={18} />
                  ) : (
                    <RefreshCw size={18} />
                  )}
                </IconButton>
              </Tooltip>
            </Stack>
          </Stack>

          <Box sx={{ mt: 3 }}>
            <InfiniteScrollList
              items={users}
              renderItem={renderUserCard}
              loadMore={loadMoreUsers}
              hasMore={hasMore}
              gridTemplateColumns={{
                xs: "1fr",
                sm: "1fr 1fr",
                md: "1fr 1fr 1fr"
              }}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
} 