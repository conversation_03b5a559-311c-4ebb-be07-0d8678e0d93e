"use client";

import { userApi } from "@/api/user-api";
import {DASHBOARD_PATH, LOGIN_PATH, PROJECT_DESCRIPTION, PROJECT_NAME} from "@/config";
import { Lock, Phone, Eye, EyeOff, Info } from "lucide-react";
import { Alert, Box, Button, Fade, IconButton, InputAdornment, Link, Paper, Stack, TextField, Typography, useMediaQuery, useTheme, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions } from "@mui/material";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType } from "@/core/components/alert";
import { useDispatch } from "react-redux";

export default function RegisterPage() {
    const router = useRouter();
    const theme = useTheme();
    const dispatch = useDispatch();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const [formData, setFormData] = useState({
        phone: "",
        password: "", 
        confirmPassword: ""
    });
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState("");
    const [openDialog, setOpenDialog] = useState(true);

    const handleCloseDialog = () => {
        setOpenDialog(false);
    };

    const handleRegister = async () => {
        setIsLoading(true);
        try {
            // 这里替换为实际的注册 API 调用
            const response = await userApi.register(formData);
            if (response.success) {
                dispatch(addAlert({ type: AlertType.SUCCESS, message: "注册成功，请登录" }));
                router.push(LOGIN_PATH);
            } else {
                setError("注册失败，请稍后重试");
            }
        } catch (err) {
            setError("注册失败，请稍后重试");
        } finally {
            setIsLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const {name, value} = e.target;
        setFormData(prev => ({...prev, [name]: value}));
        setError("");
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        // 表单验证
        if (!formData.phone || !formData.password || !formData.confirmPassword) {
            setError("请填写所有必填字段");
            return;
        }
        
        if (formData.password !== formData.confirmPassword) {
            setError("两次输入的密码不一致");
            return;
        }
        
        if (formData.password.length < 8) {
            setError("密码长度不能少于8个字符");
            return;
        }
        
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(formData.phone)) {
            setError("请输入有效的手机号码");
            return;
        }
        
        await handleRegister();
    };

    return (
        <Box
            component="main"
            sx={{
                display: "flex",
                minHeight: "100vh",
                backgroundColor: theme.palette.background.default,
                backgroundImage: `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.grey[100]} 100%)`,
                overflow: "hidden",
                px: { xs: 2, sm: 3, md: 0 }
            }}
        >
            {/* 微信提示弹窗 */}
            <Dialog
                open={openDialog}
                onClose={handleCloseDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Info size={24} color={theme.palette.info.main} />
                    <Typography variant="h6">重要提示</Typography>
                </DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        请确保填写的手机号可以添加微信，否则注册将无效。
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog} variant="contained" autoFocus>
                        我知道了
                    </Button>
                </DialogActions>
            </Dialog>

            {/* 左侧装饰区域 - 仅在非移动设备上显示 */}
            <Box
                sx={{
                    display: {xs: "none", md: "flex"},
                    width: "50%",
                    backgroundColor: theme.palette.primary.main,
                    backgroundImage: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                    justifyContent: "center",
                    alignItems: "center",
                    position: "relative",
                    overflow: "hidden",
                }}
            >
                <Box
                    sx={{
                        position: "relative",
                        zIndex: 2,
                        p: 6,
                        color: theme.palette.primary.contrastText,
                        textAlign: "center",
                        maxWidth: "600px",
                    }}
                >
                    <Typography
                        variant="h2"
                        component="h1"
                        sx={{
                            fontWeight: 700,
                            mb: 3,
                            letterSpacing: "-0.5px",
                            textShadow: "0px 2px 8px rgba(0, 0, 0, 0.1)",
                            fontSize: {xs: "2.5rem", sm: "3rem", md: "3.5rem"}
                        }}
                    >
                        {PROJECT_NAME}
                    </Typography>
                    <Typography
                        variant="h5"
                        sx={{
                            fontWeight: 500,
                            opacity: 0.9,
                            mb: 6,
                            letterSpacing: "0.5px",
                            position: "relative",
                            "&::after": {
                                content: '""',
                                position: "absolute",
                                bottom: "-20px",
                                left: "50%",
                                transform: "translateX(-50%)",
                                width: "80px",
                                height: "4px",
                                borderRadius: "2px",
                                backgroundColor: theme.palette.primary.light,
                                opacity: 0.7
                            }
                        }}
                    >
                        {PROJECT_DESCRIPTION}
                    </Typography>
                </Box>

                {/* 装饰性图形元素 */}
                <Box
                    sx={{
                        position: "absolute",
                        bottom: "-10%",
                        right: "-5%",
                        width: "300px",
                        height: "300px",
                        borderRadius: "50%",
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                    }}
                />
                <Box
                    sx={{
                        position: "absolute",
                        top: "10%",
                        left: "-5%",
                        width: "200px",
                        height: "200px",
                        borderRadius: "50%",
                        backgroundColor: "rgba(255, 255, 255, 0.05)",
                    }}
                />
            </Box>

            {/* 注册区域 */}
            <Box
                sx={{
                    flex: 1,
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    p: {xs: 2, sm: 4},
                }}
            >
                {/* 移动设备上的品牌标识 */}
                <Fade in={true} timeout={800}>
                    <Box
                        sx={{
                            display: {xs: "flex", md: "none"},
                            flexDirection: "column",
                            alignItems: "center",
                            mb: 4,
                            textAlign: "center"
                        }}
                    >
                        <Typography
                            variant="h4"
                            component="h1"
                            sx={{
                                fontWeight: 700,
                                color: theme.palette.primary.light,
                                mb: 1
                            }}
                        >
                            {PROJECT_NAME}
                        </Typography>
                        <Typography
                            variant="subtitle1"
                            sx={{
                                color: theme.palette.text.secondary,
                                fontWeight: 500,
                                position: "relative",
                                pb: 2,
                                "&::after": {
                                    content: '""',
                                    position: "absolute",
                                    bottom: 0,
                                    left: "50%",
                                    transform: "translateX(-50%)",
                                    width: "40px",
                                    height: "3px",
                                    borderRadius: "1.5px",
                                    backgroundColor: theme.palette.primary.light,
                                }
                            }}
                        >
                            {PROJECT_DESCRIPTION}
                        </Typography>
                    </Box>
                </Fade>

                {/* 注册卡片 */}
                <Fade in={true} timeout={1000}>
                    <Paper
                        elevation={3}
                        sx={{
                            width: "100%",
                            maxWidth: "450px",
                            borderRadius: "16px",
                            p: {xs: 3, sm: 4},
                            backgroundColor: theme.palette.background.paper,
                        }}
                    >
                        <Typography
                            variant="h5"
                            component="h2"
                            sx={{
                                mb: 3,
                                fontWeight: 600,
                                color: theme.palette.text.primary
                            }}
                        >
                            创建账号
                        </Typography>

                        <form onSubmit={handleSubmit}>
                            <Stack spacing={3}>
                                {error && (
                                    <Alert severity="error" sx={{borderRadius: "8px"}}>
                                        {error}
                                    </Alert>
                                )}

                                <TextField
                                    fullWidth
                                    required
                                    id="phone"
                                    name="phone"
                                    label="手机号码"
                                    variant="outlined"
                                    value={formData.phone}
                                    onChange={handleInputChange}
                                    helperText="请输入可添加微信的手机号"
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <Phone size={20} />
                                            </InputAdornment>
                                        ),
                                    }}
                                />

                                <TextField
                                    fullWidth
                                    required
                                    id="password"
                                    name="password"
                                    label="密码"
                                    type={showPassword ? "text" : "password"}
                                    variant="outlined"
                                    value={formData.password}
                                    onChange={handleInputChange}
                                    helperText="密码长度至少8个字符"
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <Lock size={20} />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    aria-label="切换密码可见性"
                                                    onClick={() => setShowPassword(!showPassword)}
                                                    edge="end"
                                                >
                                                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                                                </IconButton>
                                            </InputAdornment>
                                        ),
                                    }}
                                />

                                <TextField
                                    fullWidth
                                    required
                                    id="confirmPassword"
                                    name="confirmPassword"
                                    label="确认密码"
                                    type={showConfirmPassword ? "text" : "password"}
                                    variant="outlined"
                                    value={formData.confirmPassword}
                                    onChange={handleInputChange}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <Lock size={20} />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    aria-label="切换密码可见性"
                                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                                    edge="end"
                                                >
                                                    {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                                                </IconButton>
                                            </InputAdornment>
                                        ),
                                    }}
                                />

                                <Button
                                    fullWidth
                                    type="submit"
                                    variant="contained"
                                    color="primary"
                                    size="large"
                                    disabled={isLoading}
                                    sx={{
                                        py: 1.5,
                                        mt: 1,
                                        fontWeight: 500,
                                        fontSize: "1rem",
                                    }}
                                >
                                    {isLoading ? "注册中..." : "注册"}
                                </Button>
                            </Stack>
                        </form>

                        <Box sx={{mt: 3, textAlign: "center"}}>
                            <Typography variant="body2" sx={{mb: 2}}>
                                已有账号？ 
                                <Link 
                                    href="/public/login" 
                                    underline="hover" 
                                    sx={{ml: 1, fontWeight: 500}}
                                >
                                    登录
                                </Link>
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                {new Date().getFullYear()} {PROJECT_NAME}. 保留所有权利
                            </Typography>
                        </Box>
                    </Paper>
                </Fade>
            </Box>
        </Box>
    );
} 