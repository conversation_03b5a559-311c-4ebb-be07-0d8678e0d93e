"use client";

import React from "react";
import { useState, useEffect, useCallback } from "react";
import { useInView } from "react-intersection-observer";
import { Box, Typography, Grid } from "@mui/material";
import { Loader } from "lucide-react";

// 无限滚动钩子
export const useInfiniteScroll = ({ loadMore, hasMore = true }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const { ref, inView } = useInView({
    threshold: 1,
    rootMargin: "10px",
    triggerOnce: false,
  });

  const handleLoadMore = useCallback(async () => {
    if (loading || !hasMore) return;

    try {
      setLoading(true);
      setError(null);
      await loadMore();
    } catch (err) {
      setError(err);
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 500);
    }
  }, [loadMore, loading, hasMore]);

  useEffect(() => {
    if (inView && hasMore && !loading) {
      handleLoadMore();
    }
  }, [inView, hasMore, loading, handleLoadMore]);

  return {
    ref,
    loading,
    error,
    retry: handleLoadMore,
  };
};

// 无限滚动列表组件
export const InfiniteScrollList = ({
  items = [],
  renderItem,
  loadMore,
  hasMore,
  gridTemplateColumns = {
    xs: "1fr", // 手机上每行1个
    sm: "1fr 1fr", // 平板上每行2个
    md: "1fr 1fr 1fr", // PC上每行3个
  },
}) => {
  const { ref, loading, error, retry } = useInfiniteScroll({
    loadMore,
    hasMore,
  });

  // 空状态
  if (items.length === 0 && !loading && !error) {
    return (
      <Box sx={{ textAlign: "center", py: 4 }}>
        <Typography variant="body1" color="text.secondary">
          暂无数据
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: "100%" }}>
      {/* 列表内容 */}
      <Box sx={{ width: "100%" }}>
        <Grid
          container
          spacing={2}
          sx={{
            display: "grid",
            gridTemplateColumns: gridTemplateColumns,
            gap: 2,
          }}
        >
          {items.map((item, index) => (
            <Box
              key={index}
              sx={{
                width: "100%",
                height: "100%",
              }}
            >
              {renderItem(item, index)}
            </Box>
          ))}
        </Grid>
      </Box>

      {/* 加载更多触发器 */}
      {hasMore && (
        <Box
          ref={ref}
          sx={{
            display: "flex",
            justifyContent: "center",
            py: 3,
            width: "100%",
          }}
        >
          {loading && (
            <Box
              sx={{
                textAlign: "center",
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <Loader size={18} className="animate-spin" />
              <Typography variant="body2" color="text.secondary">
                加载中...
              </Typography>
            </Box>
          )}
        </Box>
      )}

      {/* 错误状态 */}
      {error && (
        <Box sx={{ textAlign: "center", py: 2 }}>
          <Typography variant="body2" color="error">
            加载失败
          </Typography>
          <Box sx={{ mt: 1 }}>
            <Typography
              variant="button"
              color="primary"
              sx={{ cursor: "pointer" }}
              onClick={retry}
            >
              重试
            </Typography>
          </Box>
        </Box>
      )}

      {/* 加载完成状态 */}
      {!hasMore && items.length > 0 && (
        <Box sx={{ textAlign: "center", py: 2 }}>
          <Typography variant="body2" color="text.secondary">
            没有更多数据了
          </Typography>
        </Box>
      )}
    </Box>
  );
};
