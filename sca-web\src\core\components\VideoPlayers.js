"use client"

import React, {useCallback, useEffect, useRef, useState} from 'react';
import ReactPlayer from 'react-player';
import PropTypes from 'prop-types';
import {Box, IconButton, Paper, Slider, Typography} from '@mui/material';
import {Maximize, Minimize, Pause, Play, Volume1, Volume2, VolumeX} from 'lucide-react';

/**
 * 视频播放器组件
 * 提供自定义控制栏和更多高级功能
 */
const VideoPlayer = ({
                         url,
                         width = '100%',
                         height = 'auto',
                     }) => {
    const playerRef = useRef(null);
    const containerRef = useRef(null);
    const controlsTimerRef = useRef(null);

    const [playing, setPlaying] = useState(false);
    const [volume, setVolume] = useState(1);
    const [isMuted, setIsMuted] = useState(false);
    const [played, setPlayed] = useState(0);
    const [duration, setDuration] = useState(0);
    const [seeking, setSeeking] = useState(false);
    const [fullScreen, setFullScreen] = useState(false);
    const [displayControls, setDisplayControls] = useState(false);
    const [hover, setHover] = useState(false);
    const [ready, setReady] = useState(false);
    const [error, setError] = useState(null);

    // 重置控制栏隐藏计时器
    const resetControlsTimer = useCallback(() => {
        if (controlsTimerRef.current) clearTimeout(controlsTimerRef.current);

        if (playing && !hover) {
            controlsTimerRef.current = setTimeout(() => {
                setDisplayControls(false);
            }, 2000);
        }
    }, [playing, hover]);

    // 处理鼠标移入移出
    const handleMouseMove = useCallback(() => {
        setDisplayControls(true);
        resetControlsTimer();
    }, [resetControlsTimer]);

    // 简化的事件处理函数
    const handlePlayPause = () => setPlaying(prev => !prev);
    const handleToggleMute = () => setIsMuted(prev => !prev);

    // 处理视频错误
    const handleError = useCallback((e) => {
        console.error('视频播放错误:', e);
        setError('视频加载失败，请检查网络连接或视频地址是否正确');
        setReady(false);
        setPlaying(false);
    }, []);

    // 进度控制
    const handleProgress = useCallback((state) => {
        if (!seeking) setPlayed(state.played);
    }, [seeking]);

    // 处理拖动
    const handleSeekMouseDown = () => setSeeking(true);

    const handleSeekChange = (_, newValue) => {
        setPlayed(newValue);
    };

    const handleSeekMouseUp = (_, newValue) => {
        setSeeking(false);
        playerRef.current?.seekTo(newValue);
    };

    // 音量控制
    const handleVolumeChange = (_, newValue) => {
        const newVolume = newValue;
        setVolume(newVolume);
        setIsMuted(newValue === 0);
    };

    // 全屏切换
    const handleToggleFullScreen = useCallback(() => {
        if (!containerRef.current) return;

        if (!document.fullscreenElement) {
            containerRef.current.requestFullscreen().catch(err => {
                console.error(`全屏出错: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    }, []);

    // 格式化时间
    const formatTime = (seconds) => {
        if (isNaN(seconds)) return '00:00';

        const date = new Date(seconds * 1000);
        const hh = date.getUTCHours();
        const mm = date.getUTCMinutes();
        const ss = date.getUTCSeconds().toString().padStart(2, '0');

        return hh ? `${hh}:${mm.toString().padStart(2, '0')}:${ss}` : `${mm}:${ss}`;
    };

    // 监听全屏变化
    useEffect(() => {
        const handleFullscreenChange = () => setFullScreen(!!document.fullscreenElement);
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
    }, []);

    // 清除控制栏计时器
    useEffect(() => {
        return () => controlsTimerRef.current && clearTimeout(controlsTimerRef.current);
    }, []);

    // URL 变化时重置错误状态
    useEffect(() => setError(null), [url]);

    return (
        <Box
            ref={containerRef}
            sx={{
                position: 'relative',
                width,
                height,
                bgcolor: 'common.black',
                overflow: 'hidden',
                borderRadius: 0,
            }}
            onMouseMove={handleMouseMove}
            onMouseLeave={() => playing && setDisplayControls(false)}
        >
            <ReactPlayer
                ref={playerRef}
                url={url}
                width="100%"
                height="100%"
                playing={playing}
                volume={volume}
                muted={isMuted}
                progressInterval={100}
                playsinline
                onReady={() => {
                    setReady(true);
                    setError(null);
                }}
                onPlay={() => setPlaying(true)}
                onPause={() => setPlaying(false)}
                onProgress={handleProgress}
                onDuration={setDuration}
                onError={handleError}
            />

            {/* 自定义控制栏 */}
            <Box
                sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                    padding: '30px 10px 10px',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'opacity 0.3s',
                    opacity: displayControls ? 1 : 0,
                    pointerEvents: displayControls ? 'auto' : 'none',
                }}
                onMouseEnter={() => setHover(true)}
                onMouseLeave={() => setHover(false)}
            >
                {/* 进度条 */}
                <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                    <Slider
                        size="small"
                        value={played}
                        min={0}
                        max={0.999999}
                        step={0.000001}
                        onMouseDown={handleSeekMouseDown}
                        onChange={handleSeekChange}
                        onChangeCommitted={handleSeekMouseUp}
                        sx={{
                            color: 'primary.main',
                            '& .MuiSlider-thumb': {
                                width: 12,
                                height: 12,
                                transition: '0.3s cubic-bezier(.47,1.64,.41,.8)',
                                '&:before': {
                                    boxShadow: '0 2px 12px 0 rgba(0,0,0,0.4)',
                                },
                                '&:hover, &.Mui-focusVisible': {
                                    boxShadow: '0px 0px 0px 8px rgb(255 255 255 / 16%)',
                                },
                                '&.Mui-active': {
                                    width: 16,
                                    height: 16,
                                },
                            },
                            '& .MuiSlider-rail': {
                                opacity: 0.28,
                            },
                        }}
                    />
                </Box>

                {/* 控制按钮和信息 */}
                <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                    <Box sx={{display: 'flex', alignItems: 'center'}}>
                        <IconButton onClick={handlePlayPause} size="small" sx={{color: 'common.white'}}>
                            {playing ? <Pause size={20}/> : <Play size={20}/>}
                        </IconButton>

                        <Box sx={{display: 'flex', alignItems: 'center', ml: 1}}>
                            <IconButton onClick={handleToggleMute} size="small" sx={{color: 'common.white'}}>
                                {isMuted ? <VolumeX size={18}/> : volume > 0.5 ? <Volume2 size={18}/> : <Volume1 size={18}/>}
                            </IconButton>

                            <Slider
                                size="small"
                                value={isMuted ? 0 : volume}
                                min={0}
                                max={1}
                                step={0.01}
                                onChange={handleVolumeChange}
                                sx={{
                                    width: 60,
                                    ml: 1,
                                    color: 'common.white',
                                    '& .MuiSlider-rail': {
                                        opacity: 0.28,
                                    },
                                }}
                            />
                        </Box>

                        <Typography variant="caption" sx={{color: 'common.white', ml: 2}}>
                            {formatTime(duration * played)} / {formatTime(duration)}
                        </Typography>
                    </Box>

                    <IconButton
                        onClick={handleToggleFullScreen}
                        size="small"
                        sx={{color: 'common.white'}}
                    >
                        {fullScreen ? <Minimize size={18}/> : <Maximize size={18}/>}
                    </IconButton>
                </Box>
            </Box>

            {/* 点击播放/暂停 */}
            {!error && (
                <Box
                    sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: displayControls ? '60px' : 0,
                        cursor: 'pointer'
                    }}
                    onClick={handlePlayPause}
                />
            )}

            {/* 加载指示器 */}
            {!ready && !error && (
                <Box
                    sx={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        color: 'common.white',
                    }}
                >
                    <Typography variant="body2">加载中...</Typography>
                </Box>
            )}

            {/* 大型播放按钮 */}
            {!playing && ready && !error && (
                <Box
                    sx={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        color: 'common.white',
                        opacity: 0.7,
                        cursor: 'pointer',
                        fontSize: 64,
                        '&:hover': {
                            opacity: 0.9,
                        }
                    }}
                    onClick={handlePlayPause}
                >
                    <Play size={64}/>
                </Box>
            )}

            {/* 错误提示 */}
            {error && (
                <Box
                    sx={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        maxWidth: '80%',
                    }}
                >
                    <Paper
                        elevation={3}
                        sx={{
                            p: 2,
                            bgcolor: 'error.dark',
                            color: 'common.white',
                            borderRadius: 2,
                            textAlign: 'center'
                        }}
                    >
                        <Typography variant="h6" sx={{mb: 1}}>⚠️</Typography>
                        <Typography variant="body2">{error}</Typography>
                        <Box sx={{mt: 2}}>
                            <IconButton
                                onClick={() => {
                                    setError(null);
                                    setPlaying(true);
                                }}
                                sx={{color: 'common.white', border: 1, borderColor: 'common.white'}}
                                size="small"
                            >
                                重试
                            </IconButton>
                        </Box>
                    </Paper>
                </Box>
            )}
        </Box>
    );
};

VideoPlayer.propTypes = {
    url: PropTypes.string.isRequired,
    width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

export default React.memo(VideoPlayer); 