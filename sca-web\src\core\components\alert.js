"use client"

import {removeAlert} from "@/core/components/redux/alert-slice";
import {CheckCircle, Error, Info, Warning} from "@mui/icons-material";
import {Alert as MuiAlert, Snackbar} from '@mui/material';
import {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';

export const AlertType = {
    SUCCESS: "success",
    INFO: "info",
    WARNING: "warning",
    ERROR: "error",
};

export const AlertMsg = {
    CREATE: "创建成功",
    MODIFY: "修改成功",
    DELETE: "删除成功",
};

const getAlertIcon = (type) => {
    switch (type) {
        case 'info':
            return <Info/>;
        case 'success':
            return <CheckCircle/>;
        case 'warning':
            return <Warning/>;
        case 'error':
            return <Error/>;
        default:
            return null;
    }
};

const AlertItem = ({alert, index, total}) => {
    const dispatch = useDispatch();

    useEffect(() => {
        const timer = setTimeout(() => {
            dispatch(removeAlert(alert.id));
        }, 3000);

        return () => clearTimeout(timer);
    }, [alert.id, dispatch]);

    // 计算垂直位置，根据索引向下偏移
    const topPosition = `${(index * 60) + 24}px`;

    return (
        <Snackbar
            open={true}
            anchorOrigin={{vertical: 'top', horizontal: 'center'}}
            style={{top: topPosition}}
        >
            <MuiAlert
                severity={alert.type}
                icon={getAlertIcon(alert.type)}
                sx={{
                    '& .MuiAlert-icon': {
                        fontSize: 28
                    }
                }}
                elevation={6}
                variant="filled"
            >
                {alert.message}
            </MuiAlert>
        </Snackbar>
    );
};

export default function Alert() {
    const alerts = useSelector((state) => state.alert.alerts);

    return (
        <>
            {alerts?.map((alert, index) => (
                <AlertItem 
                    key={alert.id} 
                    alert={alert} 
                    index={index}
                    total={alerts.length}
                />
            ))}
        </>
    );
}