/**
 * 音频播放器工具类
 *
 * 使用方法:
 * ```js
 * import { audioPlayer } from '@/core/tools/audio-player';
 *
 * // 播放音频
 * try {
 *   await audioPlayer.playAudio('音频文件URL');
 * } catch (error) {
 *   console.error('播放失败:', error);
 * }
 *
 * // 停止当前播放
 * await audioPlayer.stopAudio();
 * ```
 *
 * 特性:
 * - 单例模式，全局共用一个播放器实例
 * - 自动处理播放冲突，新的播放会停止当前播放
 * - Promise 接口，支持 async/await 调用
 * - 完整的错误处理
 */

import {Howl} from 'howler';

class AudioPlayer {
    constructor() {
        this.currentSound = null;
    }

    async stopAudio() {
        if (this.currentSound) {
            this.currentSound.stop();
            this.currentSound.unload();
            this.currentSound = null;
        }
    }

    handleError(error, reject) {
        console.error('音频错误:', error);
        this.stopAudio();
        reject(new Error(typeof error === 'string' ? error : '音频操作失败'));
    }

    async playAudio(src) {
        if (!src) {
            throw new Error('音频源不能为空');
        }

        // 如果当前正在播放，先停止
        if (this.currentSound) {
            await this.stopAudio();
        }

        return new Promise((resolve, reject) => {
            this.currentSound = new Howl({
                src: [src],
                html5: true,
                preload: true,
                onload: () => {
                    console.log('音频加载成功');
                },
                onplay: () => {
                    console.log('开始播放音频');
                },
                onend: () => {
                    console.log('音频播放结束');
                    this.stopAudio();
                    resolve();
                },
                onstop: () => {
                    console.log('音频播放停止');
                    resolve();
                },
                onloaderror: (id, error) => this.handleError('音频加载失败', reject),
                onplayerror: (id, error) => this.handleError('音频播放失败', reject)
            });

            try {
                this.currentSound.play();
            } catch (error) {
                this.handleError(error, reject);
            }
        });
    }
}

export const audioPlayer = new AudioPlayer(); 